package com.ruoyi.project.proposal.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Category;
import com.ruoyi.common.enums.proposal.EvaCategoryEnum;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 提案办理情况评价对象 proposal_handle_evaluations
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProposalHandleEvaluations extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String handleId;

    @Category(category = EvaCategoryEnum.BASIC_INFO, description = "是否按时对提案进行答复")
    private Boolean isTimelyResponse;

    @Category(category = EvaCategoryEnum.BASIC_INFO, description = "是否收到正式办理报告")
    private Boolean hasFormalReport;

    @Category(category = EvaCategoryEnum.BASIC_INFO, description = "是否有专门办理人员")
    private Boolean hasDedicatedHandler;

    @Category(category = EvaCategoryEnum.BASIC_INFO, description = "办理报告是否有领导签发")
    private Boolean isLeadersSignature;

    @Category(category = EvaCategoryEnum.BASIC_INFO, description = "是否有办前、办中、办后协商")
    private Boolean hasPrePostNegotiation;

    @Category(category = EvaCategoryEnum.BASIC_INFO, description = "交办是否标准")
    private Boolean isStandardHandover;

    @Category(category = EvaCategoryEnum.HANDLE_EFFECT, description = "采纳或部分采纳建议")
    private Boolean adoptOrPartiallyAdopt;

    @Category(category = EvaCategoryEnum.HANDLE_EFFECT, description = "有解决方案需跟踪落实")
    private Boolean needToImplement;

    @Category(category = EvaCategoryEnum.HANDLE_EFFECT, description = "已解决或基本解决")
    private Boolean solvedOrPartiallySolved;

    @Category(category = EvaCategoryEnum.HANDLE_EFFECT, description = "无解决方案")
    private Boolean noSolution;

    @Category(category = EvaCategoryEnum.HANDLE_EFFECT, description = "向提案者解释清楚")
    private Boolean explainedClearly;

    @Category(category = EvaCategoryEnum.HANDLE_EFFECT, description = "未向提案者解悉情况")
    private Boolean notExplained;

    @Category(category = EvaCategoryEnum.OTHER, description = "其他意见")
    private String otherOpinions;

    @Category(category = EvaCategoryEnum.HANDLE_QUALITY, description = "承办业务能力")
    private Integer businessAbility;

    @Category(category = EvaCategoryEnum.HANDLE_QUALITY, description = "沟通的主动性")
    private Integer activeCommunication;

    @Category(category = EvaCategoryEnum.HANDLE_QUALITY, description = "交流的充分性")
    private Integer sufficientExchange;

    @Category(category = EvaCategoryEnum.HANDLE_QUALITY, description = "办理的针对性")
    private Integer targetedProcessing;

    @Category(category = EvaCategoryEnum.HANDLE_QUALITY, description = "对问题的共识度")
    private Integer consensusOnProblems;

    @Category(category = EvaCategoryEnum.HANDLE_QUALITY, description = "对办理的总体评价")
    private BigDecimal overallEvaluation;

    private Boolean delFlag;


    @TableField(exist = false)
    private String remark;
}
