package com.ruoyi.project.proposal.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 承办单位 Dto
 */
@Data
public class UndertakeUnitDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "承办单位id")
    private String unitId;

    @ApiModelProperty(value = "承办单位名称")
    private String unitName;

    @ApiModelProperty(value = "承办方式")
    private UndertakeWayEnum undertakeWay;

    @ApiModelProperty(value = "办理截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadline;
}
