package com.ruoyi.project.proposal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 委员提案详细统计VO（立案/不立案分类统计）
 */
@Data
public class MemberProposalDetailStatisticsVo {

    @ApiModelProperty(value = "委员姓名")
    private String memberName;

    // 立案统计
    @ApiModelProperty(value = "立案-单独")
    private Long filedIndividualCount = 0L;

    @ApiModelProperty(value = "立案-领衔")
    private Long filedLeaderCount = 0L;

    @ApiModelProperty(value = "立案-附议")
    private Long filedMotionCount = 0L;

    @ApiModelProperty(value = "立案-小计")
    private Long filedSubtotal = 0L;

    // 不立案统计
    @ApiModelProperty(value = "不立案-单独")
    private Long notFiledIndividualCount = 0L;

    @ApiModelProperty(value = "不立案-领衔")
    private Long notFiledLeaderCount = 0L;

    @ApiModelProperty(value = "不立案-附议")
    private Long notFiledMotionCount = 0L;

    @ApiModelProperty(value = "不立案-小计")
    private Long notFiledSubtotal = 0L;

    @ApiModelProperty(value = "总计")
    private Long total = 0L;

    /**
     * 计算小计和总计
     */
    public void calculateTotals() {
        this.filedSubtotal = this.filedIndividualCount + this.filedLeaderCount + this.filedMotionCount;
        this.notFiledSubtotal = this.notFiledIndividualCount + this.notFiledLeaderCount + this.notFiledMotionCount;
        this.total = this.filedSubtotal + this.notFiledSubtotal;
    }
}
