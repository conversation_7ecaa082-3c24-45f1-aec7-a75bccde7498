package com.ruoyi.project.proposal.domain.vo;


import com.ruoyi.common.annotation.Header;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EvaluationExcelVo {

    @Header(order = 0, category = "序号")
    private Integer no;

    @Header(order = 1, category = "年度")
    private Integer year;

    @Header(order = 2, category = "提案者")
    private String proposer;

    @Header(order = 3, category = "承办单位")
    private String organizer;

    @Header(order = 4, category = "办理结果")
    private String undertakeResult;

    @Header(order = 5, category = "承办人员")
    private String undertakePerson;

    @Header(order = 6, category = "承办方式")
    private String handleWay;

    @Header(order = 7, category = "提案质量")
    private String proposalQuality;

    @Header(order = 8, category = "办理基本情况", subCategory = "是否按时对提案进行回复" )
    private String isTimelyResponse;

    @Header(order = 9, category = "办理基本情况", subCategory = "是否收到正式办理报告" )
    private String hasFormalReport;

    @Header(order = 10, category = "办理基本情况", subCategory = "是否有专门办理人员" )
    private String hasDedicatedHandler;

    @Header(order = 11, category = "办理基本情况", subCategory = "办理报告是否有领导签发" )
    private String isLeadersSignature;

    @Header(order = 12, category = "办理基本情况", subCategory = "是否有办前、办中、办后协商" )
    private String hasPrePostNegotiation;

    @Header(order = 13, category = "办理基本情况", subCategory = "交办是否标准" )
    private String isStandardHandover;

    @Header(order = 14, category = "办理效果", subCategory = "采纳或部分采纳建议")
    private String adoptOrPartiallyAdopt;

    @Header(order = 15, category = "办理效果", subCategory = "有解决方案需跟踪落实")
    private String needToImplement;

    @Header(order = 16, category = "办理效果", subCategory = "已解决或基本解决")
    private String solvedOrPartiallySolved;

    @Header(order = 17, category = "办理效果", subCategory = "无解决方案")
    private String noSolution;

    @Header(order = 18, category = "办理效果", subCategory = "向提案者解释清楚")
    private String explainedClearly;

    @Header(order = 19, category = "办理效果", subCategory = "未向提案者解释情况")
    private String notExplained;

    @Header(order = 20, category = "办理质量评价", subCategory = "承办人业务能力")
    private Integer businessAbility;

    @Header(order = 21, category = "办理质量评价", subCategory = "沟通的主动性")
    private Integer activeCommunication;

    @Header(order = 22, category = "办理质量评价", subCategory = "交流的充分性")
    private Integer sufficientExchange;

    @Header(order = 23, category = "办理质量评价", subCategory = "办理的针对性")
    private Integer targetedProcessing;

    @Header(order = 24, category = "办理质量评价", subCategory = "对问题的共识度")
    private Integer consensusOnProblems;

    @Header(order = 25, category = "办理质量评价", subCategory = "对办理的总体评价")
    private BigDecimal overallEvaluation;

    @Header(order = 26, category = "其他意见")
    private String otherOpinions;
}
