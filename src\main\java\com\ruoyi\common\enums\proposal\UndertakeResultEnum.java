package com.ruoyi.common.enums.proposal;

import lombok.Getter;

/**
 * 办理枚举
 */
@Getter
public enum UndertakeResultEnum {

    A("A:解决或基本解决"),
    B("B:列出计划两三年可解决"),
    C("C:因为条件限制一时不能解决"),
    D("D:留作参考"),
    E("E:未处理");

    private final String description;

    UndertakeResultEnum(String message) {
        this.description = message;
    }

    public static String getSolutionDesc(String value) {
        for (UndertakeResultEnum item : UndertakeResultEnum.values()) {
            if (item.name().equals(value)) {
                return item.getDescription();
            }
        }
        return null;
    }
}
