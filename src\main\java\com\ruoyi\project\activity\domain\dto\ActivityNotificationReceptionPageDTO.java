package com.ruoyi.project.activity.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.ruoyi.common.domain.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动通知分页查询DTO
 */
@Data
@ApiModel(value = "活动通知分页查询DTO")
public class ActivityNotificationReceptionPageDTO extends PageQuery {

    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer currentPage = 1;

    /**
     * 活动通知id
     */
    @NotBlank(message = "活动通知id不能为空")
    @ApiModelProperty(value = "活动通知id", required = true)
    private String notificationId;

    /**
     * 接收人姓名（模糊查询）
     */
    @ApiModelProperty(value = "接收人姓名")
    private String recipientName;

    /**
     * 是否已读
     */
    @ApiModelProperty(value = "是否已读")
    private Boolean isReaded;
}