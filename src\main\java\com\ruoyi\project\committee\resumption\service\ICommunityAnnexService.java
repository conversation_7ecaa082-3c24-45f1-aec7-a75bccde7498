package com.ruoyi.project.committee.resumption.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.committee.resumption.domain.po.CommunityAnnex;

import java.util.List;

/**
 * 公益情况附件Service接口
 */
public interface ICommunityAnnexService extends IService<CommunityAnnex> {

    /**
     * 根据公益情况ID查询附件列表
     *
     * @param communityId 公益情况ID
     * @return 附件列表
     */
    List<CommunityAnnex> selectListByCommunityId(String communityId);

    /**
     * 根据公益情况ID删除附件
     *
     * @param communityId 公益情况ID
     * @return 结果
     */
    boolean deleteByCommunityId(String communityId);
}
