package com.ruoyi.project.proposal.service;

import java.util.List;
import com.ruoyi.project.proposal.domain.ProposalVerifyRecord;
import com.ruoyi.project.proposal.domain.dto.FileCaseDto;
import com.ruoyi.project.proposal.domain.dto.MergeCaseDto;
import com.ruoyi.project.proposal.domain.vo.ProposalVerifyRecordVo;

/**
 * 提案审核记录信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
public interface IProposalVerifyRecordService {

    /**
     * 立案审核记录
     *
     * @param fileCaseDto fileCaseDto
     */
    public void addFileCaseVerifyRecord(FileCaseDto fileCaseDto);

    /**
     * 不立案审核记录
     *
     * @param fileCaseDto fileCaseDto
     */
    public void addNotFileCaseVerifyRecord(FileCaseDto fileCaseDto);

    /**
     * 合案审核记录
     *
     * @param mergeCaseDto   mergeCaseDto
     * @param serialNumberStr serialNumberStr
     */
    public void addMergeCaseVerifyRecord(MergeCaseDto mergeCaseDto, String serialNumberStr);

    /**
     * 撤案审核记录
     * @param fileCaseDto fileCaseDto
     */
    public void addCancelCaseVerifyRecord(FileCaseDto fileCaseDto);

    /**
     * 交办办审核记录
     * @param fileCaseDto fileCaseDto
     */
    public void addAssignCaseVerifyRecord(FileCaseDto fileCaseDto);

    /**
     * 查询提案审核记录信息
     * 
     * @param id 提案审核记录信息主键
     * @return 提案审核记录信息
     */
    public ProposalVerifyRecord selectProposalVerifyRecordById(Long id);

    /**
     * 查询提案审核记录信息列表
     * 
     * @param proposalVerifyRecord 提案审核记录信息
     * @return 提案审核记录信息集合
     */
    public List<ProposalVerifyRecord> selectProposalVerifyRecordList(ProposalVerifyRecord proposalVerifyRecord);

    /**
     * 根据 proposalId 查询提案审核记录信息
     * @param proposalId 提案id
     * @return 提案审核记录信息集合
     */
    public List<ProposalVerifyRecordVo> selectProposalVerifyRecordList(Long proposalId);
}
