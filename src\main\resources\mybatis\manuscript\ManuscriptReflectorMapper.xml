<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.community.mapper.ManuscriptReflectorMapper">


    <select id="getManuscriptReflectorList"
            resultType="com.ruoyi.project.community.domain.vo.ManuscriptReflectorVo">
        SELECT
            mr.user_id,
            u.user_name,
            CASE u.sex
                WHEN 0 THEN '男'
                WHEN 1 THEN '女'
            END AS sex,
            po.post_name AS post
        FROM manuscript_reflector_rel mr
            LEFT JOIN sys_user u ON mr.user_id = u.user_id
            LEFT JOIN sys_user_post p ON u.user_id = p.user_id
            LEFT JOIN sys_post po ON p.post_id = po.post_id
        WHERE mr.manuscript_id = #{manuscriptId} AND mr.del_flag = false
    </select>
</mapper>