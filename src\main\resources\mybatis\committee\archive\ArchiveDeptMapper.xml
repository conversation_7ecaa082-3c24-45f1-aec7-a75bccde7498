<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.archive.mapper.ArchiveDeptMapper">

	<resultMap type="com.ruoyi.project.committee.archive.domain.ArchiveDept" id="ArchiveDeptResult">
		<id     property="deptId"     column="dept_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="deptName"   column="dept_name"   />
		<result property="orderNum"   column="order_num"   />
		<result property="leader"     column="leader"      />
		<result property="phone"      column="phone"       />
		<result property="email"      column="email"       />
		<result property="postalCode" column="postal_code" />
		<result property="address"    column="address"     />
		<result property="status"     column="status"      />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
	</resultMap>
	
	<sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.postal_code, d.address, d.status, d.del_flag, d.create_by, d.create_time, d.update_by, d.update_time
        from sys_dept d
    </sql>
    
	<select id="selectDeptList" parameterType="com.ruoyi.project.committee.archive.domain.ArchiveDept" resultMap="ArchiveDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
		<if test="deptId != null and deptId != 0">
			AND dept_id = #{deptId}
		</if>
        <if test="parentId != null and parentId != 0">
			AND parent_id = #{parentId}
		</if>
		<if test="deptName != null and deptName != ''">
			AND dept_name like concat('%', #{deptName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND status = #{status}
		</if>
		order by d.parent_id, d.order_num
    </select>
    
    <select id="selectDeptListByAncestors" parameterType="String" resultMap="ArchiveDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        <if test="ancestors != null and ancestors != ''">
            AND ancestors = #{ancestors}
        </if>
        order by d.parent_id, d.order_num
    </select>
    
    <select id="selectDeptById" parameterType="Long" resultMap="ArchiveDeptResult">
		select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.postal_code, d.address, d.status,
			(select dept_name from sys_dept where dept_id = d.parent_id) parent_name
		from sys_dept d
		where d.dept_id = #{deptId}
	</select>
</mapper>
