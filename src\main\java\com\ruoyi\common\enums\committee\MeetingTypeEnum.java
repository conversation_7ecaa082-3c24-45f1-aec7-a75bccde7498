package com.ruoyi.common.enums.committee;

import lombok.Getter;


@Getter
public enum MeetingTypeEnum {
    FULL_COMM("全委会"),
    STANDING_COMM("常委会"),
    CHAIR_MEET("主席会议"),
    SEC_GEN_MEET("秘书长会议"),
    SPEC_COMM("专门委员会会议"),
    PARTY_GROUP("党组会议"),
    LEAD_TEAM("机关领导班子会议"),
    GOVT_BUS_FORUM("政企协商座谈会"),
    TOPIC_CONSULT("专题协商会议"),
    EXP_STAND_COMM("常委扩大会议"),
    TARGET_CONSULT("对口协商会议"),
    PROP_HANDLE_FORUM("提案办理协商座谈会"),
    OTHER("其他会议");

    private final String description;

    MeetingTypeEnum(String description) {
        this.description = description;
    }

}
