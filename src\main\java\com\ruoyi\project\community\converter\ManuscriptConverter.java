package com.ruoyi.project.community.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.community.domain.Manuscript;
import com.ruoyi.project.community.domain.ManuscriptRecord;
import com.ruoyi.project.community.domain.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;


@Mapper
public interface ManuscriptConverter {

    ManuscriptConverter INSTANCE = Mappers.getMapper(ManuscriptConverter.class);

    @Mapping(target = "reportUnit", ignore = true)
    @Mapping(target = "reportUnitDetail", ignore = true)
    @Mapping(target = "reportTarget", ignore = true)
    Manuscript convert(ManuscriptEditVo editVo);

    @Mapping(target = "reportUnit", ignore = true)
    @Mapping(target = "reportUnitDetail", ignore = true)
    @Mapping(target = "reportTarget", ignore = true)
    ManuscriptVo convertTo(Manuscript manuscript);

    @Mapping(target = "category", expression = "java(manuscript.getCategory() == null ? null : manuscript.getCategory().getDescription())")
    @Mapping(target = "status", expression = "java(manuscript.getStatus() == null ? null : manuscript.getStatus().getDescription())")
    ManuscriptReviewVo convert(Manuscript manuscript);

    @Mapping(source = "createTime", target = "auditTime")
    @Mapping(source = "createBy", target = "auditUser")
    ManuscriptRecordVo convert(ManuscriptRecord bean);

    @Mapping(target = "category", expression = "java(manuscript.getCategory() == null ? null : manuscript.getCategory().getDescription())")
    @Mapping(target = "submissionTime", qualifiedByName = "formatDate")
    ManuscriptExportVo convertToExportVo(Manuscript manuscript);

    List<ManuscriptRecordVo> convert(List<ManuscriptRecord> list);

    List<ManuscriptExportVo> convertToExportVo(List<Manuscript> list);

    Page<ManuscriptReviewVo> convert(Page<Manuscript> page);

    Page<ManuscriptPageVo> convertToPage(Page<Manuscript> page);


    @Named("formatDate")
    static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(date);
    }
}
