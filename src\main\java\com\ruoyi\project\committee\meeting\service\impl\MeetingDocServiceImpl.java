package com.ruoyi.project.committee.meeting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.meeting.converter.MeetingConverter;
import com.ruoyi.project.committee.meeting.domain.MeetingDoc;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingDocEditDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingDocPageDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingDocPageVo;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingDocVo;
import com.ruoyi.project.committee.meeting.mapper.MeetingDocMapper;
import com.ruoyi.project.committee.meeting.service.IMeetingDocService;
import com.ruoyi.project.proposal.domain.Annex;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.proposal.mapper.AnnexMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MeetingDocServiceImpl implements IMeetingDocService {

    @Resource
    private AnnexMapper annexMapper;

    @Resource
    private MeetingDocMapper meetingDocMapper;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    private void validate(Long id) {
        if (meetingDocMapper.selectById(id) == null) {
            throw new ServiceException("该会议资料不存在");
        }
    }

    @Override
    public Long saveMeetingDoc(MeetingDocEditDto createDto) {
        MeetingDoc meetingDoc = MeetingConverter.INSTANCE.convert(createDto);
        meetingDoc.setId(null);

        if (ObjectUtil.isNotEmpty(createDto.getAnnexList())) {
            meetingDoc.setAnnex(JSON.toJSONString(createDto.getAnnexList()));
        } else {
            meetingDoc.setAnnex(JSON.toJSONString(Collections.emptyList()));
        }

        meetingDocMapper.insert(meetingDoc);

        return meetingDoc.getId();
    }

    @Override
    public Boolean updateMeetingDoc(MeetingDocEditDto updateDto) {
        validate(Long.valueOf(updateDto.getId()));

        MeetingDoc updateObj = MeetingConverter.INSTANCE.convert(updateDto);
        if (ObjectUtil.isNotEmpty(updateDto.getAnnexList())) {
            updateObj.setAnnex(JSON.toJSONString(updateDto.getAnnexList()));
        } else {
            updateObj.setAnnex(JSON.toJSONString(Collections.emptyList()));
        }

        return meetingDocMapper.updateById(updateObj) > 0;
    }

    @Override
    public Boolean deleteMeetingDoc(String id) {
        validate(Long.valueOf(id));
        return meetingDocMapper.deleteById(id) > 0;
    }

    @Override
    public IPage<MeetingDocPageVo> getMeetingDocPage(MeetingDocPageDto pageDto) {
        Page<MeetingDoc> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        IPage<MeetingDoc> pageResult = meetingDocMapper.selectMeetingDoc(page, pageDto);
        return MeetingConverter.INSTANCE.convertToDocPage(pageResult);
    }

    @Override
    public MeetingDocVo getMeetingDoc(String id) {
        MeetingDoc meetingDoc = meetingDocMapper.selectById(id);
        if (ObjectUtil.isNull(meetingDoc)) {
            return null;
        }

        MeetingDocVo docVo = MeetingConverter.INSTANCE.convert(meetingDoc);
        Optional.ofNullable(docVo.getSpeaker())
                .map(speakerId -> {
                    docVo.setSpeakerId(speakerId);
                    return committeeMemberMapper.selectById(speakerId);
                })
                .ifPresent(
                        committeeMember -> docVo.setSpeaker(committeeMember.getUserName())
                );

        List<Long> annexIdList = JSON.parseArray(meetingDoc.getAnnex(), Long.class);
        if (ObjectUtil.isNotEmpty(annexIdList)) {
            List<Annex> annexes = annexMapper.selectBatchIds(annexIdList);
            List<AnnexVo> annexVoList = annexes.stream()
                    .map(annex -> {
                        AnnexVo annexVo = new AnnexVo();
                        BeanUtils.copyProperties(annex, annexVo);
                        return annexVo;
                    })
                    .collect(Collectors.toList());
            docVo.setAnnexList(annexVoList);
        } else {
            docVo.setAnnexList(Collections.emptyList());
        }

        return docVo;
    }
}
