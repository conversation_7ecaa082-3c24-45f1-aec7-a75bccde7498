package com.ruoyi.project.committee.resumption.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.AuditStatusEnum;
import com.ruoyi.common.enums.honor.AwardTypeEnum;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 获奖情况对象
 */
@Data
@TableName("t_honor")
public class Honor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "pkid", type = IdType.ASSIGN_ID)
    private String pkid;

    /**
     * 标题
     */
    private String title;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 采集时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date collectTime;

    /**
     * 创建人ID
     */
    @TableField("create_name")
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新人ID
     */
    @TableField("update_name")
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;

    /**
     * 荣誉等级
     */
    private String honorLevel;

    /**
     * 审核状态
     */
    @TableField("audi_status")
    private AuditStatusEnum audiStatus;

    /**
     * 荣誉类型
     */
    @TableField("honor_type")
    private AwardTypeEnum honorType;

    /**
     * 发布人ID
     */
    private String publishId;

    /**
     * 作者名称
     */
    private String writerName;

    /**
     * 参与者名称
     */
    private String participantsName;

    /**
     * 是否启用
     */
    private String isEnable;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 审核人电话
     */
    private String checkerPhone;

    /**
     * 审核原因
     */
    private String checkReason;

    /**
     * 区县级
     */
    private Integer districtLevel;

    /**
     * 市级
     */
    private Integer cityLevel;

    /**
     * 省级
     */
    private Integer provinceLevel;

    /**
     * 国家级
     */
    private Integer nationalLevel;
}
