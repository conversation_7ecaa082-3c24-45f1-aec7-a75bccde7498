package com.ruoyi.project.proposal.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 质量评价信息对象 proposal_quality_evaluation
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProposalQualityEvaluation extends BaseEntity {

    /** id */
    private String id;

    /** 提案id */
    private String proposalId;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String deptId;

    /** 总体评价 */
    @Excel(name = "总体评价")
    private String evaluation;

    /** 评价时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "评价时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date evaluationTime;

    /** 删除标识位 */
    private Boolean delFlag;

}
