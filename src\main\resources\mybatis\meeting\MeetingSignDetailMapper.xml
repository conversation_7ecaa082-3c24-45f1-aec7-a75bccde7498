<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.meeting.mapper.MeetingSignDetailMapper">

    <select id="selectMeetingSignDetailList"
            resultType="com.ruoyi.project.committee.meeting.domain.vo.MeetingSignDetailVo">
        SELECT
            sd.id,
            cm.user_name AS peopleName,
            sd.sign_type,
            sd.is_leave,
            sd.reason,
            sd.begin_date
        FROM meeting_sign_detail sd
            INNER JOIN sys_committee_member cm ON cm.id = sd.people_id
            LEFT JOIN meeting_participants mp ON mp.people_id = sd.people_id AND mp.del_flag = false AND mp.is_attend = true
        WHERE sd.sign_id = #{signId}
            AND sd.del_flag = false
    </select>

    <select id="getParticipantSignDetail"
            resultType="com.ruoyi.project.committee.meeting.domain.MeetingSignDetail">
        SELECT
            sd.*
        FROM meeting_sign_detail sd
        LEFT JOIN sys_committee_member cm ON cm.id = sd.people_id
        WHERE sd.del_flag = false
            AND sd.sign_id = #{signId}
            AND cm.user_id = #{currentUserId}
    </select>
</mapper>
