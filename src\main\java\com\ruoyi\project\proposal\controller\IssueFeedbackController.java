package com.ruoyi.project.proposal.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.proposal.domain.IssueFeedback;
import com.ruoyi.project.proposal.service.IIssueFeedbackService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/issueFeedback")
public class IssueFeedbackController extends BaseController {

    @Resource
    private IIssueFeedbackService issueFeedbackService;

    @GetMapping("/list")
    public TableDataInfo getUserIssueFeedbackList() {
        return getDataTable(issueFeedbackService.getFeedbackList());
    }

    @PostMapping("/add")
    public AjaxResult add(@RequestBody IssueFeedback issueFeedback) {
        return AjaxResult.success(issueFeedbackService.insertFeedback(issueFeedback));
    }

    @PostMapping("/update")
    public AjaxResult update(@RequestBody IssueFeedback issueFeedback) {
        return AjaxResult.success(issueFeedbackService.updateFeedback(issueFeedback));
    }
}
