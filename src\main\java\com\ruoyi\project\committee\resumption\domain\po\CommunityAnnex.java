package com.ruoyi.project.committee.resumption.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 公益情况附件对象
 */
@Data
@TableName("community_annex")
public class CommunityAnnex implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 公益情况ID
     */
    @TableField("community_id")
    private String communityId;

    /**
     * 附件url
     */
    private String url;

    /**
     * 附件名称
     */
    @TableField("annex_name")
    private String annexName;

    /**
     * 附件类型
     */
    @TableField("annex_type")
    private String annexType;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;
}
