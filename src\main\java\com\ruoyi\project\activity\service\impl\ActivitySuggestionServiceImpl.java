package com.ruoyi.project.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.activity.domain.ActivityBasicinfo;
import com.ruoyi.project.activity.domain.ActivitySuggestion;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionUpdateDTO;
import com.ruoyi.project.activity.domain.vo.ActivitySuggestionDetailVO;
import com.ruoyi.project.activity.domain.vo.ActivitySuggestionVO;
import com.ruoyi.project.activity.mapper.ActivityBasicinfoMapper;
import com.ruoyi.project.activity.mapper.ActivitySuggestionMapper;
import com.ruoyi.project.activity.service.IActivitySuggestionService;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 意见建议服务实现
 */
@Service
public class ActivitySuggestionServiceImpl extends ServiceImpl<ActivitySuggestionMapper, ActivitySuggestion>
        implements IActivitySuggestionService {

    @Autowired
    private ActivityBasicinfoMapper activityBasicinfoMapper;

    @Autowired
    private CommitteeMemberMapper committeeMemberMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addActivitySuggestion(ActivitySuggestionAddDTO dto) {
        // 验证活动是否存在
        ActivityBasicinfo activity = activityBasicinfoMapper.selectById(dto.getActivityId());
        if (activity == null) {
            throw new ServiceException("关联的活动不存在");
        }

        // 创建意见建议实体
        ActivitySuggestion activitySuggestion = new ActivitySuggestion();
        BeanUtils.copyProperties(dto, activitySuggestion);

        // 设置默认值
        activitySuggestion.setSubmitterId(dto.getSubmitterId());
        if (StringUtils.isBlank(dto.getAuditStatus())) {
            activitySuggestion.setAuditStatus("1");// 默认审核通过
        }

        // 保存意见建议
        return this.save(activitySuggestion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateActivitySuggestion(ActivitySuggestionUpdateDTO dto) {
        // 验证意见建议是否存在
        ActivitySuggestion existingSuggestion = this.getById(dto.getId());
        if (existingSuggestion == null) {
            throw new ServiceException("意见建议不存在");
        }

        // 验证活动是否存在
        ActivityBasicinfo activity = activityBasicinfoMapper.selectById(dto.getActivityId());
        if (activity == null) {
            throw new ServiceException("关联的活动不存在");
        }

        // 创建更新实体
        ActivitySuggestion activitySuggestion = new ActivitySuggestion();
        BeanUtils.copyProperties(dto, activitySuggestion);

        // 设置默认值
        if (StringUtils.isBlank(dto.getAuditStatus())) {
            activitySuggestion.setAuditStatus("1");// 默认审核通过
        }

        // 更新意见建议
        return this.updateById(activitySuggestion);
    }

    @Override
    public IPage<ActivitySuggestionVO> pageActivitySuggestion(ActivitySuggestionPageDTO pageDTO) {
        // 创建分页对象
        Page<ActivitySuggestion> page = new Page<>(pageDTO.getCurrentPage(), pageDTO.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<ActivitySuggestion> queryWrapper = new LambdaQueryWrapper<>();

        // 活动ID精确查询（必传参数）
        queryWrapper.eq(ActivitySuggestion::getActivityId, pageDTO.getActivityId());

        // 意见标题模糊查询
        if (StringUtils.isNotBlank(pageDTO.getTitle())) {
            queryWrapper.like(ActivitySuggestion::getTitle, pageDTO.getTitle());
        }

        // 时间查询
        if (StringUtils.isNotBlank(pageDTO.getStartTime())) {
            queryWrapper.ge(ActivitySuggestion::getCreateTime, pageDTO.getStartTime());
        }
        if (StringUtils.isNotBlank(pageDTO.getEndTime())) {
            queryWrapper.le(ActivitySuggestion::getCreateTime, pageDTO.getEndTime());
        }

        // 按创建时间降序排列
        queryWrapper.orderByDesc(ActivitySuggestion::getCreateTime);

        // 执行分页查询
        IPage<ActivitySuggestion> suggestionPage = this.page(page, queryWrapper);

        // 如果没有数据，返回空分页对象
        if (suggestionPage.getRecords() == null || suggestionPage.getRecords().isEmpty()) {
            return new Page<ActivitySuggestionVO>(pageDTO.getCurrentPage(), pageDTO.getPageSize());
        }

        // 获取所有关联的活动ID
        List<Long> activityIds = suggestionPage.getRecords().stream()
                .map(ActivitySuggestion::getActivityId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询活动信息
        Map<Long, ActivityBasicinfo> activityMap = activityBasicinfoMapper.selectBatchIds(activityIds)
                .stream()
                .collect(Collectors.toMap(ActivityBasicinfo::getId, Function.identity()));

        // 获取所有关联的发表人ID
        List<Long> submitterIds = suggestionPage.getRecords().stream()
                .map(ActivitySuggestion::getSubmitterId)
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询委员信息
        final Map<Long, CommitteeMember> memberMap;
        if (!submitterIds.isEmpty()) {
            memberMap = committeeMemberMapper.selectBatchIds(submitterIds)
                    .stream()
                    .collect(Collectors.toMap(CommitteeMember::getId, Function.identity()));
        } else {
            memberMap = new HashMap<>();
        }

        // 转换为VO对象
        List<ActivitySuggestionVO> voList = suggestionPage.getRecords().stream().map(suggestion -> {
            ActivitySuggestionVO vo = new ActivitySuggestionVO();
            BeanUtils.copyProperties(suggestion, vo);

            // 设置活动标题
            ActivityBasicinfo activity = activityMap.get(suggestion.getActivityId());
            if (activity != null) {
                vo.setActivityTitle(activity.getTitle());
            }

            // 设置发表人姓名
            if (StringUtils.isNotBlank(suggestion.getSubmitterId())) {
                try {
                    Long submitterId = Long.valueOf(suggestion.getSubmitterId());
                    CommitteeMember member = memberMap.get(submitterId);
                    if (member != null) {
                        vo.setSubmitterName(member.getUserName());
                    }
                } catch (NumberFormatException e) {
                    // 如果submitterId不是有效的数字，保持原有的submitterName
                }
            }

            // 设置审核状态文本
            vo.setAuditStatusText(getAuditStatusText(suggestion.getAuditStatus()));

            return vo;
        }).collect(Collectors.toList());

        // 创建VO分页对象并设置数据
        Page<ActivitySuggestionVO> voPage = new Page<>(suggestionPage.getCurrent(), suggestionPage.getSize(),
                suggestionPage.getTotal());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    public ActivitySuggestionDetailVO getActivitySuggestionDetail(Long id) {
        // 查询意见建议信息
        ActivitySuggestion suggestion = this.getById(id);
        if (suggestion == null) {
            throw new ServiceException("意见建议不存在");
        }

        // 转换为详情VO
        ActivitySuggestionDetailVO detailVO = new ActivitySuggestionDetailVO();
        BeanUtils.copyProperties(suggestion, detailVO);

        // 查询关联的活动信息
        if (suggestion.getActivityId() != null) {
            ActivityBasicinfo activity = activityBasicinfoMapper.selectById(suggestion.getActivityId());
            if (activity != null) {
                detailVO.setActivityTitle(activity.getTitle());
            }
        }

        // 查询发表人信息
        if (StringUtils.isNotBlank(suggestion.getSubmitterId())) {
            try {
                Long submitterId = Long.valueOf(suggestion.getSubmitterId());
                CommitteeMember member = committeeMemberMapper.selectById(submitterId);
                if (member != null) {
                    detailVO.setSubmitterName(member.getUserName());
                }
            } catch (NumberFormatException e) {
                // 如果submitterId不是有效的数字，保持原有的submitterName
            }
        }

        // 设置审核状态文本
        detailVO.setAuditStatusText(getAuditStatusText(suggestion.getAuditStatus()));

        return detailVO;
    }

    /**
     * 获取审核状态文本
     *
     * @param auditStatus 审核状态
     * @return 审核状态文本
     */
    private String getAuditStatusText(String auditStatus) {
        if (StringUtils.isEmpty(auditStatus)) {
            return "";
        }
        switch (auditStatus) {
            case "0":
                return "待审核";
            case "1":
                return "审核通过";
            case "2":
                return "审核不通过";
            case "3":
                return "暂存";
            default:
                return "";
        }
    }
}
