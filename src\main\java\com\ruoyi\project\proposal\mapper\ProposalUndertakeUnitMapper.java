package com.ruoyi.project.proposal.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalUndertakeUnit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProposalUndertakeUnitMapper extends BaseMapper<ProposalUndertakeUnit> {

    default List<ProposalUndertakeUnit> selectByUnitId(Long unitId) {
        return selectList(new LambdaQueryWrapper<ProposalUndertakeUnit>()
                .eq(ProposalUndertakeUnit::getUnitId, unitId)
        );
    }

    ProposalUndertakeUnit selectUndertakeUnitByProposalId(@Param("proposalId")String proposalId, @Param("unitId") Long unitId);

    List<ProposalUndertakeUnit> selectUndertakeUnitList(@Param("proposalId")String proposalId);

}
