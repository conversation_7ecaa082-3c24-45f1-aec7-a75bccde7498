package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 活动签到明细批量签到DTO
 */
@Data
@ApiModel(value = "活动签到明细批量签到DTO")
public class ActivitySignDetailBatchDTO {

    /**
     * 签到ID
     */
    @NotBlank(message = "签到ID不能为空")
    @ApiModelProperty(value = "签到ID", required = true)
    private String signPkid;
    
    /**
     * 需要签到的人员ID列表
     */
    @NotEmpty(message = "签到人员列表不能为空")
    @ApiModelProperty(value = "需要签到的人员ID列表", required = true)
    private List<String> peoplePkids;
    
    /**
     * 签到备注
     */
    @ApiModelProperty(value = "签到备注")
    private String remark;
}
