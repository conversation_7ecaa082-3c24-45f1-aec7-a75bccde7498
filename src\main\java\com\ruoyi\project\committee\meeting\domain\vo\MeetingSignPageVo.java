package com.ruoyi.project.committee.meeting.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MeetingSignPageVo {

    private String id;

    private String detailId;

    @ApiModelProperty(value = "会议名称")
    private String meetingName;

    @ApiModelProperty(value = "签到说明")
    private String reason;

    @ApiModelProperty(value =  "发起人")
    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "签到开始时间")
    private Date signBeginTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "签到结束时间")
    private Date signEndTime;

    @ApiModelProperty(value = "是否签到")
    private Boolean isSign;

    @ApiModelProperty(value = "是否请假")
    private Boolean isLeave;
}
