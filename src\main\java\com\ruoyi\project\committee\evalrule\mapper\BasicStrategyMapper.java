package com.ruoyi.project.committee.evalrule.mapper;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface BasicStrategyMapper {

    /**
     * 缺席区政协全体会议1次（未请假）
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAbsencePlenaryUnexcused(@Param("year") String year);

    /**
     * 缺席区政协全体会议1次（请假）
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAbsencePlenaryExcused(@Param("year") String year);

    /**
     * 缺席区政协常委会议1次（未请假）
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAbsenceStandingUnexcused(@Param("year") String year);

    /**
     * 缺席区政协常委会议1次（请假）
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAbsenceStandingExcused(@Param("year") String year);

    /**
     * 缺席区政协专委会会议1次（未请假）
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAbsenceCommitteeUnexcused(@Param("year") String year);

    /**
     * 缺席区政协专委会会议1次（请假）
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAbsenceCommitteeExcused(@Param("year") String year);

    /**
     * 区政协委员列席区政协常委扩大会议
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAttendanceStandingExtended(@Param("year") String year);

    /**
     * 参加专委会组织的其他会议（非全体会议）
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAttendanceCommitteeOther(@Param("year") String year);

    /**
     * 参加全国政协、省政协、市政协组织的相关会议
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAttendanceHigherMeeting(@Param("year") String year);

    /**
     * 参加全国政协、省政协、市政协、区政协组织相关活动
     * 
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleAttendanceMultiActivity(@Param("year") String year);

    /**
     * 受区政协委托参加区委、区政府组成部门等单位组织的活动
     * 
     * @param member 政协委员对象
     *               remark >> 没有该类型的活动
     */
    // Integer handleAttendanceDelegatedExternal(@Param("member") CommitteeMember
    // member);

    /**
     * 参加区政协组织的委员培训活动
     * 
     * @param member 政协委员对象
     *               remark >> 没有该类型的活动
     */
    // Integer handleTrainingDistrictRegular(@Param("member") CommitteeMember
    // member);

    /**
     * 参加与区政协工作相关的各类会议与活动情况
     *
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleParticipationRelatedAll(@Param("year") String year);

    /**
     * 处理全会/常委会/联组会议的书面+口头发言
     *
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleSpeechPlenaryBoth(@Param("year") String year);

    /**
     * 处理全会/常委会/联组会议的书面发言
     *
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleSpeechPlenaryWritten(@Param("year") String year);

    /**
     * 处理其他会议的发言（按次数计）
     *
     * @param year 年份
     */
    @MapKey("member_id")
    Map<Long, Integer> handleSpeechOtherCounted(@Param("year") String year);

    /**
     * 一年内是否未提交提案
     *
     * @param year 年份
     * @return result
     */
    @MapKey("member_id")
    Map<Long, Boolean> hasNotSubmittedProposalWithinOneYear(@Param("year") String year);

    /**
     * 获取委员获立案的提案数量（第一提案人）
     *
     * @param year 年份
     * @return result
     */
    @MapKey("member_id")
    Map<Long, Integer> countApprovedProposalsAsPrimaryProposer(@Param("year") String year);

    /**
     * 获取委员获立案的提案数量（附议人）
     *
     * @param year 年份
     * @return result
     */
    @MapKey("member_id")
    Map<Long, Integer> countApprovedProposalsAsSecondaryProposer(@Param("year") String year);

    /**
     * 一年内是否未提交社情民意
     *
     * @param year 年份
     * @return result
     */
    @MapKey("member_id")
    Map<Long, Boolean> hasNotSubmittedManuscriptWithinOneYear(@Param("year") String year);

    /**
     * 获取委员被采纳的社情民意数量
     *
     * @param year 年份
     * @return result
     */
    @MapKey("member_id")
    Map<Long, Integer> countAcceptedManuscripts(@Param("year") String year);

    /**
     * 获取得到区领导批示的稿件数量
     *
     * @param year 年份
     * @return result
     */
    @MapKey("member_id")
    Map<Long, Integer> countDistrictEndorsedManuscript(@Param("year") String year);

    /**
     * 获取得到市领导批示的稿件数量
     *
     * @param year 年份
     * @return result
     */
    @MapKey("member_id")
    Map<Long, Integer> countCityEndorsedManuscript(@Param("year") String year);

    /**
     * 获取得到省领导批示的稿件数量
     *
     * @param year 年份
     * @return result
     */
    @MapKey("member_id")
    Map<Long, Integer> countProvinceEndorsedManuscript(@Param("year") String year);
}
