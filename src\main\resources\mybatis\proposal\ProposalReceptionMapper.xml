<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalReceptionMapper">
    
    <resultMap type="ProposalReception" id="ProposalReceptionResult">
        <result property="id"    column="id"    />
        <result property="proposalId"    column="proposal_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="joinTime"    column="join_time"    />
        <result property="receiveTime"    column="receive_time"    />
        <result property="receiveStatus"    column="receive_status"    />
        <result property="undertakeWay"    column="undertake_way"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectProposalReceptionVo">
        select id, proposal_id, dept_id, join_time, receive_time, receive_status, undertake_way, del_flag from proposal_reception
    </sql>

    <select id="selectProposalReceptionList" resultType="com.ruoyi.project.proposal.domain.vo.ProposalReceptionVo">
        SELECT
            pr.id,
            pr.proposal_id,
            u.user_id,
            u.user_name AS dept_name,
            pr.join_time,
            pr.receive_time,
            pr.receive_status,
            pr.undertake_way
        FROM proposal_reception pr
            LEFT JOIN sys_user u ON u.user_id = pr.recipient_id
        WHERE pr.del_flag = false
            AND pr.proposal_id = #{proposalId}
        ORDER BY
            CASE pr.undertake_way
                WHEN 'LEAD_OFFICE' THEN 1
                WHEN 'ASSISTANT_OFFICE' THEN 2
                ELSE 3
            END
    </select>

    <select id="selectReceptionByProposalIds" resultType="com.ruoyi.project.proposal.domain.vo.ProposalReceptionVo">
        SELECT
            pr.id,
            pr.proposal_id,
            u.user_id,
            u.user_name AS dept_name,
            pr.join_time,
            pr.receive_time,
            pr.receive_status,
            pr.undertake_way
        FROM proposal_reception pr
                 LEFT JOIN sys_user u ON u.user_id = pr.recipient_id
        WHERE pr.del_flag = false
          AND pr.proposal_id IN
            <foreach item="id" collection="idList" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>


    <delete id="deleteProposalReceptionByIds" parameterType="String">
        delete from proposal_reception where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>