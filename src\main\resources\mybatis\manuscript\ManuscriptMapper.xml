<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.community.mapper.ManuscriptMapper">

    <select id="getAuditPage" resultType="com.ruoyi.project.community.domain.Manuscript">
        SELECT
            m.id,
            m.title,
            m.category,
            m.reflect_unit,
            m.reflector,
            m.submission_time,
            m.source,
            m.status AS status
        FROM manuscript m
        WHERE m.status = 'AUDITING' AND m.del_flag = false
        <if test="pageParam.title != null and pageParam.title != ''">
            AND m.title LIKE CONCAT('%', #{pageParam.title}, '%')
        </if>
        <if test="pageParam.category != null">
            AND m.category = #{pageParam.category}
        </if>
        <if test="pageParam.categoryDetail != null">
            AND m.category_detail = #{pageParam.categoryDetail}
        </if>
        <if test="pageParam.reflector != null and pageParam.reflector != ''">
            AND m.reflector LIKE CONCAT('%', #{pageParam.reflector}, '%')
        </if>
        <if test="pageParam.reflectUnit != null and pageParam.reflectUnit != ''">
            AND m.reflect_unit LIKE CONCAT('%', #{pageParam.reflectUnit}, '%')
        </if>
        ORDER BY m.create_time DESC
    </select>

    <select id="selectManuscriptStatistics"
            resultType="com.ruoyi.project.community.domain.vo.ManuscriptStatisticsVo">
        SELECT
            mr.user_id,
            SUM(CASE WHEN mc.author_count = 1 THEN 1 ELSE 0 END) AS alone_count,
            SUM(CASE WHEN mc.author_count > 1 THEN 1 ELSE 0 END) AS joint_count
        FROM manuscript_reflector_rel mr
            JOIN manuscript m ON mr.manuscript_id = m.id
            JOIN (
                    SELECT
                        manuscript_id,
                        COUNT(user_id) AS author_count
                    FROM
                        manuscript_reflector_rel
                    GROUP BY manuscript_id
            ) mc ON mr.manuscript_id = mc.manuscript_id
        WHERE
            m.year = #{statisticsDto.year}
        <if test="statisticsDto.month != null and statisticsDto.month &gt; 0 and statisticsDto.month &lt;= 12">
            AND MONTH(m.create_time) = #{statisticsDto.month}
        </if>
        <if test="statisticsDto.startDate != null">
            AND m.create_time &gt;= #{statisticsDto.startDate}
        </if>
        <if test="statisticsDto.startDate != null">
            AND m.create_time &lt;= #{statisticsDto.endDate}
        </if>
        GROUP BY mr.user_id
    </select>
</mapper>