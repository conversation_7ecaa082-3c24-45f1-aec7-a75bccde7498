package com.ruoyi.project.proposal.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalFeedbackAnnex;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ProposalFeedbackAnnexMapper extends BaseMapper<ProposalFeedbackAnnex> {

    List<AnnexVo> selectFeedbackAnnexList(String id);

    default void deletedFeedbackAnnex(String id) {
        delete(new LambdaQueryWrapper<ProposalFeedbackAnnex>().eq(ProposalFeedbackAnnex::getFeedbackId, id));
    };
}
