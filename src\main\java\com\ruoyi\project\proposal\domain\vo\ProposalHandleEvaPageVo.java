package com.ruoyi.project.proposal.domain.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.util.Date;
import java.util.List;

@Data
public class ProposalHandleEvaPageVo {

    private String id;

    private String proposalId;

    @ApiModelProperty(value = "提案年度")
    private Long year;

    @ApiModelProperty(value = "提案案号")
    private String caseNumber;

    @ApiModelProperty(value = "提案案由")
    private String caseReason;

    @ApiModelProperty(value = "提案者")
    private String proposer;

    @ApiModelProperty(value = "承办单位")
    private String organizers;

    /** 处理结果 */
    @ApiModelProperty(value = "处理结果")
    private String undertakeResult;

    /** 承办人员 */
    @ApiModelProperty(value = "承办人员")
    private String undertakePerson;

    /** 办理方式 */
    @ApiModelProperty(value = "办理方式")
    private String handleWay;

    /** 提案质量 */
    @ApiModelProperty(value = "提案质量")
    private String proposalQuality;

    /** 办结日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "办结日期")
    private Date undertakeTime;

    /** 评价状态(0-未评价,1-已评价) */
    @ApiModelProperty(value = "评价状态(0-未评价,1-已评价)")
    private Integer evaluationStatus;

    @ApiModelProperty(value = "承办单位")
    private List<String> organizerList;
}
