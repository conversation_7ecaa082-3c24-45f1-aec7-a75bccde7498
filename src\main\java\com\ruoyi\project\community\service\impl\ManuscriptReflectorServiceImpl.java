package com.ruoyi.project.community.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.project.community.domain.ManuscriptReflector;
import com.ruoyi.project.community.domain.vo.ManuscriptReflectorVo;
import com.ruoyi.project.community.mapper.ManuscriptReflectorMapper;
import com.ruoyi.project.community.service.IManuscriptReflectorService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ManuscriptReflectorServiceImpl implements IManuscriptReflectorService {

    @Resource
    private ManuscriptReflectorMapper manuscriptReflectorMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchManuscriptReflector(String manuscriptId, List<String> reflectorIdList) {
        if (ObjectUtil.isEmpty(reflectorIdList)) {
            return;
        }

        reflectorIdList.forEach(reflectorId -> {
            ManuscriptReflector manuscriptReflector = new ManuscriptReflector();
            manuscriptReflector.setManuscriptId(manuscriptId);
            manuscriptReflector.setUserId(reflectorId);
            manuscriptReflectorMapper.insert(manuscriptReflector);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateManuscriptReflector(String manuscriptId, List<String> reflectorIdList) {
        manuscriptReflectorMapper.deleteManuscriptReflector(manuscriptId);

        saveBatchManuscriptReflector(manuscriptId, reflectorIdList);
    }

    @Override
    public List<ManuscriptReflectorVo> getManuscriptReflectorList(String manuscriptId) {
        return manuscriptReflectorMapper.getManuscriptReflectorList(manuscriptId);
    }

    @Override
    public Set<String> getUserManuscriptIds(String userId) {

        List<ManuscriptReflector> userManuscripts = manuscriptReflectorMapper.getUserManuscript(userId);

        return userManuscripts.stream()
                .map(ManuscriptReflector::getManuscriptId).collect(Collectors.toSet());
    }
}
