package com.ruoyi.project.committee.resumption.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityAuditDto;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityEditDto;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityPageDto;
import com.ruoyi.project.committee.resumption.domain.vo.PriorityPageVo;
import com.ruoyi.project.committee.resumption.service.IPriorityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 重点工作信息
 */
@Api(tags = "重点工作信息管理")
@RestController
@RequestMapping("/committee/resumption/priority")
public class PriorityController extends BaseController {

    @Autowired
    private IPriorityService priorityService;

    /**
     * 查询重点工作信息分页
     */
    @ApiOperation("查询重点工作信息分页")
    @PreAuthorize("@ss.hasPermi('committee:resumption:priority:list')")
    @PostMapping("/page")
    public TableDataInfo getPage(@RequestBody PriorityPageDto pageDto) {
        IPage<PriorityPageVo> pageResult = priorityService.selectPriorityPage(pageDto);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 查询我的重点工作信息分页
     */
    @ApiOperation("查询我的重点工作信息分页")
    @PreAuthorize("@ss.hasPermi('committee:resumption:priority:myList')")
    @PostMapping("/myPage")
    public TableDataInfo getMyPage(@RequestBody PriorityPageDto pageDto) {
        IPage<PriorityPageVo> pageResult = priorityService.selectMyPriorityPage(pageDto);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 获取重点工作信息详细信息
     */
    @ApiOperation("获取重点工作信息详细信息")
    @PreAuthorize("@ss.hasPermi('committee:resumption:priority:query')")
    @GetMapping
    public AjaxResult getInfo(@RequestParam("id") String id) {
        return success(priorityService.selectPriorityById(id));
    }

    /**
     * 提交重点工作信息
     */
    @ApiOperation("提交重点工作信息")
    @PreAuthorize("@ss.hasPermi('committee:resumption:priority:add')")
    @PostMapping
    public AjaxResult submit(@RequestBody PriorityEditDto createDto) {
        return AjaxResult.success(priorityService.submitPriority(createDto));
    }

    /**
     * 修改重点工作信息
     */
    @ApiOperation("修改重点工作信息")
    @PreAuthorize("@ss.hasPermi('committee:resumption:priority:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody PriorityEditDto editDto) {
        return AjaxResult.success(priorityService.updatePriority(editDto));
    }

    /**
     * 删除重点工作信息
     */
    @ApiOperation("删除重点工作信息")
    @PreAuthorize("@ss.hasPermi('committee:resumption:priority:remove')")
    @DeleteMapping
    public AjaxResult remove(@Valid @RequestBody PriorityAuditDto auditDto) {
        return toAjax(priorityService.deletePriorityByIds(auditDto.getIdList()));
    }

    /**
     * 审核重点工作信息
     */
    @ApiOperation("审核重点工作信息")
    @PreAuthorize("@ss.hasPermi('committee:resumption:priority:audit')")
    @PutMapping("/audit")
    public AjaxResult audit(@Valid @RequestBody PriorityAuditDto auditDto) {
        return toAjax(priorityService.auditPriority(auditDto));
    }

    @ApiOperation("退回重点工作信息详细信息")
    @PreAuthorize("@ss.hasPermi('committee:resumption:priority:return')")
    @PutMapping("/return")
    public AjaxResult returnPriority(@RequestBody PriorityAuditDto auditDto) {
        return success(priorityService.returnPriority(auditDto));
    }

}
