package com.ruoyi.project.committee.resumption.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 公益情况新增DTO
 */
@Data
@ApiModel("公益情况新增DTO")
public class CommunityAddDTO {

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date collectTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("社区类型（1-办好事，2-解难题，3-做公益）")
    private String communityType;

    @ApiModelProperty("参与者名称")
    private String participantsName;

    @ApiModelProperty("作者名称")
    private String writerName;

    @ApiModelProperty("附件列表")
    private List<CommunityAnnexDTO> annexList;

    @ApiModelProperty("参与者列表")
    private List<String> participantList;
}
