package com.ruoyi.common.enums.manuscript;

import lombok.Getter;

@Getter
public enum CategoryDetailEnum {
    /**
     * 宏观经济
     * 公共文化服务
     * 新闻舆论
     * 台湾
     * 经济体制改革
     * 外交
     * 劳动就业
     * 科技与创新
     * 收入分配
     * 法律法规
     * 医疗卫生
     * 行政管理体制
     * 财税金融
     * 文化创新
     * 人大和立法
     * 循环资源
     * 对外开放
     * 民族
     * 农业农村农民
     * 气候变化与节能减排
     * 司法
     * 基层政权和基层民主
     * 统战政协
     * 产业发展与结构调整
     * 基本经济制度
     * 社会道德与核心价值体系
     * 住房保障
     * 港澳
     * 宗教
     * 其他
     * 文化人才
     * 社会保障
     * 防灾减灾
     * 能源资源
     * 反腐倡廉
     * 侨务
     * 区域协调和城镇化
     * 生态保护与修复
     * 社会管理
     * 体育
     * 教育
     * 食品药品安全
     * 环境保护
     * 对外文化交流
     * 文化体制改革
     * 国防与国家安全
     */
    MACRO_ECONOMY("宏观经济"),
    PUBLIC_CULTURAL_SERVICES("公共文化服务"),
    NEWS_AND_PUBLIC_OPINION("新闻舆论"),
    TAIWAN("中国台湾"),
    ECONOMIC_SYSTEM_REFORM("经济体制改革"),
    DIPLOMACY("外交"),
    LABOR_AND_EMPLOYMENT("劳动就业"),
    SCIENCE_AND_INNOVATION("科技与创新"),
    INCOME_DISTRIBUTION("收入分配"),
    LAWS_AND_REGULATIONS("法律法规"),
    HEALTHCARE("医疗卫生"),
    ADMINISTRATIVE_MANAGEMENT_REFORM("行政管理体制"),
    FINANCE_AND_TAX("财税金融"),
    CULTURAL_INNOVATION("文化创新"),
    LEGISLATURE_AND_LAWMAKING("人大和立法"),
    CYCLIC_RESOURCES("循环资源"),
    OPENNESS_TO_OUTSIDE("对外开放"),
    ETHNIC_GROUPS("民族"),
    AGRICULTURE_RURAL_AND_FARMERS("农业农村农民"),
    CLIMATE_CHANGE_AND_ENERGY_SAVING("气候变化与节能减排"),
    JUDICIARY("司法"),
    LOCAL_GOVERNMENT_AND_DEMOCRACY("基层政权和基层民主"),
    UNIFIED_FRONT_AND_CPPCC("统战政协"),
    INDUSTRY_DEVELOPMENT_AND_STRUCTURE_ADJUSTMENT("产业发展与结构调整"),
    BASIC_ECONOMIC_SYSTEM("基本经济制度"),
    SOCIAL_MORALITY_AND_CORE_VALUE_SYSTEM("社会道德与核心价值体系"),
    HOUSING_SECURITY("住房保障"),
    HONG_KONG_MACAO("港澳"),
    RELIGION("宗教"),
    OTHER("其他"),
    CULTURAL_TALENTS("文化人才"),
    SOCIAL_SECURITY("社会保障"),
    DISASTER_PREVENTION_AND_REDUCTION("防灾减灾"),
    ENERGY_RESOURCES("能源资源"),
    ANTI_CORRUPTION("反腐倡廉"),
    OVERSEAS_CHINESE_AFFAIRS("侨务"),
    REGIONAL_COORDINATION_AND_URBANIZATION("区域协调和城镇化"),
    ECOLOGICAL_PROTECTION_AND_RESTORE("生态保护与修复"),
    SOCIAL_MANAGEMENT("社会管理"),
    SPORTS("体育"),
    EDUCATION("教育"),
    FOOD_AND_DRUG_SAFETY("食品药品安全"),
    ENVIRONMENTAL_PROTECTION("环境保护"),
    CULTURAL_EXCHANGE_WITH_FOREIGN_COUNTRIES("对外文化交流"),
    CULTURAL_SYSTEM_REFORM("文化体制改革"),
    NATIONAL_DEFENSE_AND_NATIONAL_SECURITY("国防与国家安全");

    private final String description;

    CategoryDetailEnum(String description) {
        this.description = description;
    }

    public static CategoryDetailEnum getByName(String enumName) {
        if (enumName == null) {
            return null;
        }

        for (CategoryDetailEnum value : values()) {
            if (value.name().equals(enumName)) {
                return value;
            }
        }
        return null;
    }
}
