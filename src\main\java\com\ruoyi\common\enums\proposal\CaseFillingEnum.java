package com.ruoyi.common.enums.proposal;

import lombok.Getter;

/**
 * 立案情况枚举
 */
@Getter
public enum CaseFillingEnum {

    // 立案
    PUT_ON("立案"),

    // 已撤案
    WITHDRAW("已撤案"),

    // 不立案
    NOT_PUT_ON("不立案"),

    // 已并案
    MERGED("已并案"),

    // 待立案
    WAIT_PUT_ON("待立案"),

    // 待办理
    WAIT_HANDLE("待办理"),

    // 办结
    FINISH("办结");

    private final String description;

    CaseFillingEnum(String description) {
        this.description = description;
    }
}
