name: tianya-java
services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    image: swr.cn-south-1.myhuaweicloud.com/tsz/tianya-java:latest
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    ports:
      - "8080:8080"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      
  db:
    image: mysql:8
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=tianya
      - MYSQL_USER=tianya
      - MYSQL_PASSWORD=tianya
      - LANG=C.UTF-8
    volumes:
      - ./sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  redis:
    image: redis:6.0.6
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 5s
      retries: 5
      