package com.ruoyi.project.committee.evalrule.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 规则信息DTO
 */
@Data
@ApiModel(value = "规则信息DTO")
public class RuleInfoEditDto {

    @ApiModelProperty(value = "主键ID")
    private String pkid;

    @ApiModelProperty(value = "父级节点id")
    private String rulePkid;

    @ApiModelProperty(value =  "规则id")
    private String rootPkid;

    @ApiModelProperty(value = "节点层数")
    private String isRoot;

    @ApiModelProperty(value = "分值")
    private String score;

    @ApiModelProperty(value = "关联数据")
    private String associated;

    @ApiModelProperty(value = "封顶最小分值")
    private String limitMinScore;

    @ApiModelProperty(value =  "封顶最大分值")
    private String limitMaxScore;

    @ApiModelProperty(value = "是否自主填报项")
    private Boolean isDeclare;

    @ApiModelProperty(value =  "排序号")
    private Integer sort;

    @ApiModelProperty(value = "规则描述")
    private String remark;
}
