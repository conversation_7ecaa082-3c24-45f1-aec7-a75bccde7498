package com.ruoyi.project.community.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ManuscriptExportVo {

    /** id */
    private String id;

    /** 标题 */
    private String title;

    /** 类别 */
    private String category;

    /** 内容 */
    private String content;

    /** 来稿时间 */
    private String submissionTime;

    /** 反馈人 */
    private String reflector;
}
