package com.ruoyi.project.committee.evalrule.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("rule_strategy")
public class RuleStrategy {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String strategyKey;

    private String strategyName;

    private String ruleType;

    private String module;

    private String executeMethod;

    private Boolean isEnabled;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
