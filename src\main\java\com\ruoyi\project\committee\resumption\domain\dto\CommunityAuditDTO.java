package com.ruoyi.project.committee.resumption.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 公益情况审核DTO
 */
@Data
@ApiModel("公益情况审核DTO")
public class CommunityAuditDTO {

    @NotEmpty(message = "公益情况ID不能为空")
    @ApiModelProperty("公益情况ID列表，支持单个或多个ID")
    private List<String> ids;

    @NotBlank(message = "审核人不能为空")
    @ApiModelProperty("审核人")
    private String checker;

    @NotBlank(message = "审核人电话不能为空")
    @ApiModelProperty("审核人电话")
    private String checkerPhone;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @ApiModelProperty("审核理由")
    private String checkReason;
}
