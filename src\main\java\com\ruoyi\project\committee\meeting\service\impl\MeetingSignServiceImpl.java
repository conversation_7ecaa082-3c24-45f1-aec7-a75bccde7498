package com.ruoyi.project.committee.meeting.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.enums.committee.SignTypeEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.meeting.converter.MeetingConverter;
import com.ruoyi.project.committee.meeting.domain.MeetingInfo;
import com.ruoyi.project.committee.meeting.domain.MeetingParticipants;
import com.ruoyi.project.committee.meeting.domain.MeetingSign;
import com.ruoyi.project.committee.meeting.domain.MeetingSignDetail;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignEditDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignPageDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingSignPageVo;
import com.ruoyi.project.committee.meeting.mapper.MeetingMapper;
import com.ruoyi.project.committee.meeting.mapper.MeetingParticipantsMapper;
import com.ruoyi.project.committee.meeting.mapper.MeetingSignMapper;
import com.ruoyi.project.committee.meeting.service.IMeetingSignDetailService;
import com.ruoyi.project.committee.meeting.service.IMeetingSignService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 会议签到信息 Service业务层处理
 */
@Service
public class MeetingSignServiceImpl implements IMeetingSignService {

    @Resource
    private MeetingMapper meetingMapper;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    @Resource
    private MeetingSignMapper meetingSignMapper;

    @Resource
    private MeetingParticipantsMapper meetingParticipantsMapper;

    @Resource
    private IMeetingSignDetailService meetingSignDetailService;

    @Override
    public IPage<MeetingSignPageVo> getMeetingSignPage(MeetingSignPageDto pageDto) {

        MeetingInfo meetingInfo = meetingMapper.selectById(pageDto.getMeetingId());
        if (ObjectUtil.isNull(meetingInfo)) {
            return PageUtils.emptyPage();
        }

        Page<MeetingSign> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        IPage<MeetingSign> meetingSignPage = meetingSignMapper.getMeetingSignPage(page, pageDto);
        Page<MeetingSignPageVo> pageResult = MeetingConverter.INSTANCE.convertToSignPage(meetingSignPage);

        pageResult.getRecords().forEach(record -> {
            record.setMeetingName(meetingInfo.getMeetingName());
        });

        return pageResult;
    }

    @Override
    public IPage<MeetingSignPageVo> getMyMeetingSignPage(MeetingSignPageDto pageDto) {

        MeetingInfo meetingInfo = meetingMapper.selectById(pageDto.getMeetingId());
        if (meetingInfo == null || DateUtil.compare(meetingInfo.getEndTime(), DateUtil.date()) < 0) {
            return PageUtils.emptyPage();
        }

        Long currentUserId = null;
        try {
            currentUserId = SecurityUtils.getUserId();
        } catch (Exception e) {
            return PageUtils.emptyPage();
        }

        MeetingParticipants participants = meetingParticipantsMapper.getMeetingParticipantsByUserId(Long.valueOf(pageDto.getMeetingId()), currentUserId);
        if (ObjectUtil.isNull(participants) || !participants.getIsAttend()) {
            return PageUtils.emptyPage();
        }

        Page<MeetingSignPageVo> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        return meetingSignMapper.getMyMeetingSignPage(page, pageDto, currentUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMeetingSign(MeetingSignEditDto createDto) {
        MeetingSign meetingSign = MeetingConverter.INSTANCE.convert(createDto);

        meetingSignMapper.insert(meetingSign);

        List<MeetingParticipants> participantList = meetingParticipantsMapper.getParticipantsByMeetingId(meetingSign.getMeetingId());
        if (ObjectUtil.isNotEmpty(participantList)) {
            List<MeetingSignDetail> signDetailList = participantList.stream().map(participant -> {
                MeetingSignDetail signDetail = new MeetingSignDetail();
                signDetail.setMeetingId(meetingSign.getMeetingId());
                signDetail.setSignId(meetingSign.getId());
                signDetail.setPeopleId(participant.getPeopleId());
                signDetail.setIsSign(false);
                signDetail.setIsLeave(false);
                signDetail.setSignType(SignTypeEnum.UNSIGNED);
                return signDetail;
            }).collect(Collectors.toList());
            meetingSignDetailService.saveBatchSignDetail(signDetailList);
        }

        return meetingSign.getId();
    }
}
