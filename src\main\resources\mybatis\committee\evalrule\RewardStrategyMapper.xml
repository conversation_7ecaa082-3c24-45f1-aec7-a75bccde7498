<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.evalrule.mapper.RewardStrategyMapper">

    <select id="handleEnvProtectionSupervisionOutstanding" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'OUTSTANDING'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'ECO_MONITOR'
    </select>

    <select id="handleEnvProtectionSupervisionGood" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'GOOD'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'ECO_MONITOR'
    </select>

    <select id="handleEnvProtectionSupervisionParticipated" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'PARTICIPANT'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'ECO_MONITOR'
    </select>
    
    <select id="handleRuralRevitalizationOutstanding" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'OUTSTANDING'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'RURAL_DEVELOP'
    </select>
    <select id="handleRuralRevitalizationGood" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'GOOD'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'RURAL_DEVELOP'
    </select>
    
    <select id="handleRuralRevitalizationParticipated" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'PARTICIPANT'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'RURAL_DEVELOP'
    </select>

    <select id="handleGrassrootsDemocracyOutstanding" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'OUTSTANDING'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'GRASSROOTS_NEG'
    </select>

    <select id="handleGrassrootsDemocracyGood" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'GOOD'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'GRASSROOTS_NEG'
    </select>

    <select id="handleGrassrootsDemocracyParticipated" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'PARTICIPANT'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'GRASSROOTS_NEG'
    </select>

    <select id="handleReadingActivitiesOutstanding" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'OUTSTANDING'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'READING_PROMOTION'
    </select>

    <select id="handleReadingActivitiesGood" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'GOOD'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'READING_PROMOTION'
    </select>

    <select id="handleReadingActivitiesParticipated" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'PARTICIPANT'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'READING_PROMOTION'
    </select>

    <select id="handleEpidemicPreventionOutstanding" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'OUTSTANDING'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'EPIDEMIC_PREVENT'
    </select>

    <select id="handleEpidemicPreventionGood" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'GOOD'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'EPIDEMIC_PREVENT'
    </select>

    <select id="handleEpidemicPreventionParticipated" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'PARTICIPANT'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'EPIDEMIC_PREVENT'
    </select>

    <select id="handleProjectServiceOutstanding" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'OUTSTANDING'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'PROJECT_ASSIST'
    </select>

    <select id="handleProjectServiceGood" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'GOOD'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'PROJECT_ASSIST'
    </select>

    <select id="handleProjectServiceParticipated" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'PARTICIPANT'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'PROJECT_ASSIST'
    </select>

    <select id="handleBusinessEnvironmentOutstanding" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'OUTSTANDING'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'BUSINESS_ENV'
    </select>

    <select id="handleBusinessEnvironmentGood" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'GOOD'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'BUSINESS_ENV'
    </select>

    <select id="handleBusinessEnvironmentParticipated" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'PARTICIPANT'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'BUSINESS_ENV'
    </select>

    <select id="handleOtherTasksOutstanding" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'OUTSTANDING'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'OTHER_TASK'
    </select>

    <select id="handleOtherTasksGood" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'GOOD'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'OTHER_TASK'
    </select>

    <select id="handleOtherTasksParticipated" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_priority p
        INNER JOIN t_examine_participants ep ON p.id = ep.report_pkid
            AND ep.committee_pkid = CONCAT(#{member.id}, '')
            AND ep.report_type = 'KEY_WORK'
            AND ep.person_type = 'PARTICIPANT'
            AND ep.del_flag = FALSE
        WHERE p.del_flag = FALSE
            AND YEAR( p.create_time) = #{member.year}
            AND p.priority_type = 'OTHER_TASK'
    </select>

</mapper>