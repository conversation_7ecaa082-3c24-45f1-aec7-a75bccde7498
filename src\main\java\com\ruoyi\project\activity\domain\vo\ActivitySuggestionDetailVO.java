package com.ruoyi.project.activity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 意见建议详情VO
 */
@Data
@ApiModel(value = "意见建议详情VO")
public class ActivitySuggestionDetailVO {

    /**
     * 自增主键
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 关联活动ID
     */
    @ApiModelProperty(value = "关联活动ID")
    private Long activityId;

    /**
     * 活动标题
     */
    @ApiModelProperty(value = "活动标题")
    private String activityTitle;

    /**
     * 意见标题
     */
    @ApiModelProperty(value = "意见标题")
    private String title;

    /**
     * 意见关键词
     */
    @ApiModelProperty(value = "意见关键词")
    private String keywords;

    /**
     * 意见内容
     */
    @ApiModelProperty(value = "意见内容")
    private String content;

    /**
     * 发表人姓名
     */
    @ApiModelProperty(value = "发表人姓名")
    private String submitterName;

    /**
     * 发表人ID
     */
    @ApiModelProperty(value = "发表人ID")
    private String submitterId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 审核状态(0-待审核,1-审核通过,2-审核不通过)
     */
    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    /**
     * 审核状态文本
     */
    @ApiModelProperty(value = "审核状态文本")
    private String auditStatusText;

    /**
     * 审核人ID
     */
    @ApiModelProperty(value = "审核人ID")
    private String auditUserId;

    /**
     * 审核人姓名
     */
    @ApiModelProperty(value = "审核人姓名")
    private String auditUserName;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 审核备注
     */
    @ApiModelProperty(value = "审核备注")
    private String auditRemark;

    /**
     * 状态(0-删除,1-正常)
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
