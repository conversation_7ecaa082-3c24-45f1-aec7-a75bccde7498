package com.ruoyi.project.committee.archive.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MemberStatisticsVo {

    private String id;

    private String userId;

    @ApiModelProperty(value = "委员名称")
    private String memberName;

    @ApiModelProperty(value = "界别")
    private String sector;

    @ApiModelProperty(value = "是否为常委会组成人员")
    private Boolean isScm;

    @ApiModelProperty(value = "全会")
    private Integer generalMeeting;

    @ApiModelProperty(value = "常委会")
    private Integer standingCommittee;

    @ApiModelProperty(value = "各类协商会")
    private Integer consultativeConferences;

    @ApiModelProperty(value = "其他会议")
    private Integer otherMeetings;

    @ApiModelProperty(value = "委员提案")
    private Integer proposals;

    @ApiModelProperty(value = "大会发言")
    private Integer confSpeech;

    @ApiModelProperty(value = "社情民意数量")
    private Integer opinions;

    @ApiModelProperty(value = "参加活动")
    private Integer joinActivity;

    @ApiModelProperty(value = "调研报告")
    private Integer researchReports;

    @ApiModelProperty(value = "发表文章")
    private Integer publishArticle;

    @ApiModelProperty(value = "重点工作")
    private Integer keyTasks;

    @ApiModelProperty(value = "获奖情况")
    private Integer awards;

    @ApiModelProperty(value = "公益情况")
    private Integer charity;

}
