package com.ruoyi.project.community.domain.dto;

import com.ruoyi.common.enums.manuscript.EndorseTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ManuscriptResponseDTO {

    @ApiModelProperty(value = "稿件id")
    private String manuscriptId;

    @ApiModelProperty(value = "反馈/批示")
    private String text;

    @ApiModelProperty(value = "批示类型")
    private List<EndorseTypeEnum> endorseTypeList;
}
