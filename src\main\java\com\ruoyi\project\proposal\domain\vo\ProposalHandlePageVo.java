package com.ruoyi.project.proposal.domain.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.CaseTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProposalHandlePageVo {

    private String id;

    private String proposalId;

    @ApiModelProperty(value = "提案年度")
    private Long year;

    @ApiModelProperty(value = "提案案号")
    private String caseNumber;

    @ApiModelProperty(value = "提案案由")
    private String caseReason;

    @ApiModelProperty(value = "提案类别")
    private CaseTypeEnum caseType;

    @ApiModelProperty(value = "提案者")
    private String proposer;

    @ApiModelProperty(value = "承办单位")
    private String organizers;

    @ApiModelProperty(value = "承办方式")
    private String undertakeWay;

    @ApiModelProperty(value = "办理结果")
    private String undertakeResult;

    @ApiModelProperty(value = "办理方式")
    private String handleWay;

    @ApiModelProperty(value = "交办日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date joinTime;

    @ApiModelProperty(value = "办理期限")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadline;

    @ApiModelProperty(value = "反馈状态")
    private Boolean feedbackStatus;

    @ApiModelProperty(value = "退回状态")
    private Boolean revertStatus;

    @ApiModelProperty(value = "办理状态")
    private Boolean handleStatus;

    @ApiModelProperty(value = "承办单位")
    private List<String> organizerList;
}
