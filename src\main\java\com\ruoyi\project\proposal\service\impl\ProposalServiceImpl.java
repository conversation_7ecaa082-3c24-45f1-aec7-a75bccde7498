package com.ruoyi.project.proposal.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HtmlUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.common.enums.proposal.MeasureEnum;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;
import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.security.LoginUser;
import com.ruoyi.project.community.service.IManuscriptService;
import com.ruoyi.project.proposal.converter.ProposalConverter;
import com.ruoyi.project.proposal.domain.ProposalHandle;
import com.ruoyi.project.proposal.domain.ProposalUserRel;
import com.ruoyi.project.proposal.domain.dto.*;
import com.ruoyi.project.proposal.domain.vo.*;
import com.ruoyi.project.proposal.mapper.ProposalHandleMapper;
import com.ruoyi.project.proposal.service.*;
import com.ruoyi.project.system.domain.SysRole;
import com.ruoyi.project.system.domain.SysUser;
import com.ruoyi.project.system.mapper.SysRoleMapper;

import com.ruoyi.project.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Service;
import com.ruoyi.project.proposal.mapper.ProposalMapper;
import com.ruoyi.project.proposal.domain.Proposal;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 提案信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@Slf4j
@Service
public class ProposalServiceImpl implements IProposalService {

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private ProposalMapper proposalMapper;

    @Resource
    private IProposalUserRelService proposalUserRelService;

    @Resource
    private IProposalAnnexRelService proposalAnnexRelService;

    @Resource
    private IProposalSupervisionRecordService supervisionRecordService;

    @Resource
    private ProposalHandleMapper proposalHandleMapper;

    @Resource
    private IProposalUndertakeUnitService proposalUndertakeUnitService;

    @Resource
    private IProposalVerifyRecordService proposalVerifyRecordService;

    @Resource
    private IProposalMergeService proposalMergeService;

    @Resource
    private IProposalReceptionService proposalReceptionService;

    @Resource
    private IAnnexService annexService;

    @Resource
    private IManuscriptService manuscriptService;

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 查询提案信息
     *
     * @param id 提案信息主键
     * @return 提案信息
     */
    @Override
    public ProposalVo selectProposalById(String id) {
        Proposal proposal = proposalMapper.selectById(id);

        ProposalVo proposalVo = ProposalConverter.INSTANCE.convert(proposal);
        if (!Objects.isNull(proposalVo.getSubmitType()) && proposal.getSubmitType().equals(SubmitTypeEnum.COLLECTIVE)) {
            // TODO: 需要确定 >> 提案者为集体时，需要展示的数据内容
            proposalVo.setProposerList(new ArrayList<>());
        } else {
            List<ProposalUserRelVo> proposalUserList = proposalUserRelService.selectProposalUserListById(id);
            proposalVo.setUserList(proposalUserList);
        }

        return proposalVo;
    }

    /**
     * 查询提案信息列表分页
     *
     * @param pageParam 查询参数
     * @return ProposalPageVo
     */
    @Override
    public IPage<ProposalPageVo> selectProposalPage(ProposalPageParamVo pageParam) {
        Page<ProposalPageVo> page = new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize());
        IPage<ProposalPageVo> proposalPageVoIPage = proposalMapper.selectProposalPage(page, pageParam, null);
        for (ProposalPageVo record : proposalPageVoIPage.getRecords()) {
            String proposer = record.getProposer();
            record.setProposer(getProposer(proposer));
        }
        return proposalPageVoIPage;
    }

    @Override
    public IPage<ProposalPageVo> selectPostProcessPage(ProposalPageParamVo pageParam) {
        Page<ProposalPageVo> page = new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize());
        List<CaseFillingEnum> caseFillingList = Arrays.asList(
                CaseFillingEnum.WAIT_PUT_ON, CaseFillingEnum.PUT_ON, CaseFillingEnum.NOT_PUT_ON, CaseFillingEnum.WITHDRAW, CaseFillingEnum.MERGED
        );
        IPage<ProposalPageVo> proposalPageVoIPage = proposalMapper.selectProposalPage(page, pageParam, caseFillingList);
        for (ProposalPageVo record : proposalPageVoIPage.getRecords()) {
            String proposer = record.getProposer();
            record.setProposer(getProposer(proposer));
        }
        return proposalPageVoIPage;
    }

    /**
     * 新增提案信息
     *
     * @param proposalEditVo 提案信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertProposal(ProposalEditVo proposalEditVo) {

        Proposal proposal = ProposalConverter.INSTANCE.convert(proposalEditVo);
        proposal.setCaseFiling(CaseFillingEnum.WAIT_PUT_ON);
        // 生成流水号
        Integer serialNumber = proposalMapper.selectSerialNumber();
        proposal.setSerialNumber(serialNumber);
        proposal.setCaseNumber(null);

        List<ProposerVo> proposerList = proposalEditVo.getCasePerson();
        proposal.setProposer(getProposer(proposerList));
        int result = proposalMapper.insert(proposal);

        if (!CollectionUtils.isEmpty(proposerList)) {
            List<ProposalUserRel> proposalUserList = new ArrayList<>();
            for (ProposerVo proposerVo : proposerList) {
                ProposalUserRel proposalUserRel = new ProposalUserRel();
                proposalUserRel.setProposalId(proposal.getId());
                proposalUserRel.setProposerId(proposerVo.getProposerId());
                proposalUserRel.setSubmitType(proposerVo.getSubmitType());
                proposalUserList.add(proposalUserRel);
            }
            proposalUserRelService.saveBatch(proposalUserList);
        }

        if (!CollectionUtils.isEmpty(proposalEditVo.getAnnexList())) {
            try {
                proposalAnnexRelService.insertProposalAnnexRel(Long.valueOf(proposal.getId()), proposalEditVo.getAnnexList());
            } catch (Exception exception) {
                log.error("Failed to save proposal annex relations: ", exception);
                throw exception;
            }
        }

        return result;
    }

    /**
     * 修改提案信息
     *
     * @param proposalEditVo 提案信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateProposal(ProposalEditVo proposalEditVo) {
        Proposal proposal = proposalMapper.selectById(proposalEditVo.getId());
        if (ObjectUtil.isNotEmpty(proposal)) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            List<SysRole> userRoles = sysRoleMapper.selectRolesByUserName(loginUser.getUsername());

            if (SecurityUtils.isAdmin(userRoles) || proposal.getCreateBy().equals(loginUser.getUsername())) {
                Proposal updateObj = ProposalConverter.INSTANCE.convert(proposalEditVo);

                if (proposalMapper.existsByCaseNumber(proposalEditVo.getCaseNumber())) {
                    throw new ServiceException(String.format("案号：%s已存在", proposalEditVo.getCaseNumber()));
                }


                proposalUserRelService.updateProposalUserRel(proposalEditVo.getId(), proposalEditVo.getCasePerson());

                proposalAnnexRelService.updateProposalAnnexRel(Long.valueOf(proposal.getId()), proposalEditVo.getAnnexList());
                updateObj.setProposer(getProposer(proposalEditVo.getCasePerson()));
                return proposalMapper.updateById(updateObj);
            } else {
                return -2;
            }
        }

        return 0;
    }

    @Override
    public int updateProposalAssignStatus(String[] ids) {
        Integer result = proposalMapper.batchUpdateProposalAssignStatus(ids);
        if (Objects.isNull(result)) {
            result = 0;
        }
        return result;
    }


    /**
     * 删除提案信息信息
     *
     * @param id 提案信息主键
     * @return 结果
     */
    @Override
    public int deleteProposalById(Long id) {
        return proposalMapper.deleteById(id);
    }

    @Override
    public ProposalWordVo selectProposalWordVoById(String id) {

        String secondedPerson = "%s\t电话：%s";

        ProposalWordVo proposalWordVo = proposalMapper.selectProposalWordVoById(id);
        String caseContent = HtmlUtil.unescape(proposalWordVo.getCaseContent())
                .replaceAll("</p>", "\r\n")
                .replaceAll("<br/>", "\r\n");
        proposalWordVo.setCaseContent(HtmlUtil.cleanHtmlTag(caseContent));

        List<ProposalUserRelVo> userList = proposalUserRelService.selectProposalUserListById(id);
        if (!CollectionUtils.isEmpty(userList)) {

            ProposalUserRelVo casePerson = userList.get(0);
            proposalWordVo.setCasePerson(casePerson.getUserName());
            proposalWordVo.setPosition(casePerson.getPosition());
            proposalWordVo.setAddress(casePerson.getCompanyAddress());
            proposalWordVo.setTelephone(casePerson.getTelephone());
            proposalWordVo.setPostalCode(casePerson.getCompanyPostalCode());

            if (userList.size() > 1) {
                ProposalUserRelVo proposalUserRelVo = userList.get(1);
                secondedPerson = String.format(secondedPerson, proposalUserRelVo.getUserName(), proposalUserRelVo.getTelephone());
                proposalWordVo.setSecondedPerson(secondedPerson);
            }
        }

        return proposalWordVo;
    }

    @Override
    public IPage<ProposalComprehensivePageVo> selectComprehensiveProposalPage(ProposalPageParamVo pageParam) {
        Page<ProposalComprehensivePageVo> page = new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize());

//        Set<String> ids = null;
//        if (ObjectUtil.isNotEmpty(pageParam.getUserId())) {
//            ids = proposalUserRelService.selectByProposerId(pageParam.getUserId());
//            if (ObjectUtil.isEmpty(ids)) {
//                return new Page<>();
//            }
//        }

        if (ObjectUtil.isNotEmpty(pageParam.getUserId())) {
            SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(pageParam.getUserId()));
            if (ObjectUtil.isNotEmpty(sysUser)) {
                pageParam.setProposer(sysUser.getUserName());
            }
        }

        IPage<ProposalComprehensivePageVo> proposalPageVoIPage = proposalMapper.selectComprehensiveProposalPage(page, pageParam, null);

        for (ProposalComprehensivePageVo record : proposalPageVoIPage.getRecords()) {
            String proposer = record.getProposer();
            record.setProposer(getProposer(proposer));
            record.setOrganizers(getOrganizer(record.getOrganizers()));
        }

        return proposalPageVoIPage;
    }

    @Override
    public ProposalComprehensiveVo selectComprehensiveInfoById(String id) {
        ProposalComprehensiveVo comprehensiveVo = proposalMapper.selectComprehensiveInfoById(id);

        if (!Objects.isNull(comprehensiveVo)) {
            if (!Objects.isNull(comprehensiveVo.getSubmitType()) && comprehensiveVo.getSubmitType().equals(SubmitTypeEnum.COLLECTIVE)) {
                // TODO: 需要确定 >> 提案者为集体时，需要展示的数据内容
                comprehensiveVo.setProposerList(new ArrayList<>());
            } else {
                List<ProposalUserRelVo> proposalUserList = proposalUserRelService.selectProposalUserListById(id);
                comprehensiveVo.setUserList(proposalUserList);
            }
        }

        List<String> organizerList = proposalHandleMapper.getOrganizerListByProposalId(id);
        if (!CollectionUtils.isEmpty(organizerList)) {
            comprehensiveVo.setOrganizers(getOrganizer(organizerList));
        }

        return comprehensiveVo;
    }

    @Override
    public IPage<ProposalAssignPageVo> selectProposalAssignPage(ProposalPageParamVo pageParam) {
        Page<ProposalAssignPageVo> page = new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize());
        IPage<ProposalAssignPageVo> assignPage = proposalMapper.selectProposalAssignPage(page, pageParam);

        // 处理承办单位
        List<String> idList = assignPage.getRecords().stream()
                .map(ProposalAssignPageVo::getId).collect(Collectors.toList());
        Map<String, List<ProposalReceptionVo>> organizerMap = proposalReceptionService.selectReceptionByIds(idList);

        for (ProposalAssignPageVo record : assignPage.getRecords()) {
            String proposer = record.getProposer();
            record.setProposer(getProposer(proposer));

            List<ProposalReceptionVo> receptions = organizerMap.get(record.getId());
            if (ObjectUtil.isNotEmpty(receptions)) {
                StringBuilder organizer = new StringBuilder();

                for (ProposalReceptionVo reception : receptions) {
                    organizer.append(reception.getDeptName());
                    organizer.append("[").append(UndertakeWayEnum.getDescription(reception.getUndertakeWay())).append("]");
                    if (reception.getReceiveStatus()) {
                        organizer.append("[").append("已接收").append("]、");
                    } else {
                        organizer.append("[").append("未接收").append("]、");
                    }
                }
                organizer.deleteCharAt(organizer.length() - 1);
                record.setOrganizer(organizer.toString());
            }

            record.setOrganizer(getOrganizer(record.getOrganizer()));
        }
        return assignPage;
    }

    private String getOrganizer(String organizer) {
        if (ObjectUtil.isEmpty(organizer)) {
            return null;
        }
        String[] parts = organizer.split(",");

        return Arrays.stream(parts).map(String::trim)
                .findFirst()
                .map(first -> parts.length == 1 ? first : first + "等")
                .orElse("");
    }


    @Override
    public int batchDeleteProposal(String[] ids) {
        if (ArrayUtils.isNotEmpty(ids)) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            List<SysRole> userRoles = sysRoleMapper.selectRolesByUserName(loginUser.getUsername());
            if (SecurityUtils.isAdmin(userRoles)) {
                return proposalMapper.batchDeleteProposal(Arrays.asList(ids));
            } else {
                List<String> removeIds = new ArrayList<>();
                List<Proposal> proposalList = proposalMapper.selectList(new LambdaQueryWrapper<Proposal>()
                        .in(Proposal::getId, ids));
                for (Proposal proposal : proposalList) {
                    if (proposal.getCreateBy().equals(loginUser.getUsername())) {
                        removeIds.add(proposal.getId());
                    }
                }
                if (removeIds.size() != ids.length) {
                    return -1;
                }

                return proposalMapper.batchDeleteProposal(removeIds);
            }

        }
        return 0;
    }

    @Override
    public Map<String, String> getProposerMap(List<String> proposalIdList) {

        Map<String, String> proposerMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(proposalIdList)) {
            List<Map<String, Object>> proposalList = proposalMapper.getProposerMap(proposalIdList);
            for (Map<String, Object> map : proposalList) {
                String proposalId = map.get("id").toString();
                String proposer = map.get("user_name").toString();

                if (!proposerMap.containsKey(proposalId)) {
                    proposerMap.put(proposalId, proposer);
                } else {
                    proposerMap.put(proposalId, proposerMap.get(proposalId) + "、" + proposer);
                }
            }
            return proposerMap;
       }

        return proposerMap;
    }

    @Override
    public IPage<SupervisionPageVo> getSupervisionPage(ProposalPageParamVo pageParam) {
        Page<SupervisionPageVo> page = new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize());
        IPage<SupervisionPageVo> supervisionPage = proposalMapper.getSupervisionPage(page, pageParam);

        for (SupervisionPageVo record : supervisionPage.getRecords()) {
            String proposer = record.getProposer();
            record.setProposer(getProposer(proposer));
        }
        return supervisionPage;
    }

    @Override
    public Integer urge(String id) {
        supervisionRecordService.addSupervisionRecord(id);
        return proposalMapper.urge(id);
    }

    @Override
    public Long getProposalCount(Integer year) {
        return proposalMapper.getProposalCount(year);
    }

    @Override
    public IPage<ProposalComprehensivePageVo> getUserProposal(ProposalPageParamVo pageParam) {
        
        LoginUser loginUser = SecurityUtils.getLoginUser();
        pageParam.setUsername(loginUser.getUsername());
        Long proposerRelId = loginUser.getUserId();
        
        Page<ProposalComprehensivePageVo> page = new Page<>(pageParam.getCurrentPage(), pageParam.getPageSize());
        return proposalMapper.getUserProposal(page, pageParam, proposerRelId);
    }

    @Override
    public Boolean deleteProposal(String id) {
        Proposal proposal = proposalMapper.selectById(id);
        if (ObjectUtil.isNotEmpty(proposal)) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (proposal.getProposer().contains(loginUser.getUsername())) {
                return proposalMapper.deleteById(id) > 0;
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean fileCase(FileCaseDto fileCaseDto) {
        Proposal proposal = proposalMapper.selectById(fileCaseDto.getProposalId());
        if (ObjectUtil.isEmpty(proposal)) {
            throw new ServiceException("该提案不存在");
        }

        if (proposalMapper.existsByCaseNumber(Integer.valueOf(fileCaseDto.getCaseNumber()))) {
            throw new ServiceException("案号已存在");
        }

        proposalMapper.update(null, new LambdaUpdateWrapper<Proposal>()
                .set(Proposal::getCaseFiling, CaseFillingEnum.PUT_ON)
                .set(Proposal::getCaseNumber, fileCaseDto.getCaseNumber())
                .eq(Proposal::getId, proposal.getId()));
        proposalVerifyRecordService.addFileCaseVerifyRecord(fileCaseDto);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean notFileCase(FileCaseDto fileCaseDto) {

        Proposal proposal = proposalMapper.selectById(fileCaseDto.getProposalId());
        if (ObjectUtil.isEmpty(proposal)) {
            throw new ServiceException("该提案不存在");
        }

        if (ObjectUtil.isNotEmpty(fileCaseDto.getMeasure())) {
            if (fileCaseDto.getMeasure().contains(MeasureEnum.TO_OPINION)) {
                // 转社情民意
                ProposalToManuscriptDto toManuscriptDto = ProposalConverter.INSTANCE.convertToManuscript(proposal);

                List<ProposalUserRelVo> proposalUserRelVos = proposalUserRelService.selectProposalUserListById(proposal.getId());
                toManuscriptDto.setProposerList(proposalUserRelVos);

                List<AnnexVo> annexVoList = annexService.selectAnnexByProposalId(Long.valueOf(proposal.getId()));
                toManuscriptDto.setAnnexIdList(annexVoList);
                manuscriptService.proposalToManuscript(toManuscriptDto);
            }
            // ...其他措施
        }

        proposalVerifyRecordService.addNotFileCaseVerifyRecord(fileCaseDto);
        proposalMapper.changeCaseFiling(fileCaseDto.getProposalId(), CaseFillingEnum.NOT_PUT_ON);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean mergeCase(MergeCaseDto mergeCaseDto) {

        if (ObjectUtil.isEmpty(mergeCaseDto.getMergeCaseList())) {
            throw new ServiceException("请选择要合并的提案");
        }

        if (proposalMapper.existsByCaseNumber(mergeCaseDto.getCaseNumber())) {
            throw new ServiceException("案号已存在");
        }

        List<Integer> caseNumberList = mergeCaseDto.getMergeCaseList().stream()
                .map(MergeCaseDetailDto::getCaseNumber)
                .collect(Collectors.toList());
        // 案号校验
        checkCaseNumber(caseNumberList);

        // 1. 更新提案信息
        String serialStr = mergeCaseDto.getMergeCaseList()
                .stream()
                .filter(item -> item.getSerialNumber() != null)
                .map(item ->String.format("%04d", item.getSerialNumber()))
                .collect(Collectors.joining("、"));
        String mainCaseReason = String.format("(由流水号%s并入)", serialStr);

        proposalMapper.mergeProposal(mergeCaseDto.getMainCaseId(), mergeCaseDto.getCaseNumber(), mainCaseReason, CaseFillingEnum.PUT_ON);

        // 2. 修改并案信息
        //   2.1 修改提案案号和提案状态
        String caseReason = String.format("(并入%04d号)", mergeCaseDto.getCaseNumber());
        for (MergeCaseDetailDto item : mergeCaseDto.getMergeCaseList()) {
            proposalMapper.mergeProposal(item.getCaseId(), item.getCaseNumber(), caseReason, CaseFillingEnum.MERGED);
        }

        // 3. 添加审核日志
        proposalVerifyRecordService.addMergeCaseVerifyRecord(mergeCaseDto, serialStr);
        // 4. 插入并案信息表
        proposalMergeService.addMergeInfo(mergeCaseDto);

        return true;
    }

    private void checkCaseNumber(List<Integer> caseNumList) {

        List<Proposal> dataList = proposalMapper.selectList(new LambdaQueryWrapper<Proposal>()
                .eq(Proposal::getYear, DateUtil.year(DateUtil.date()))
                .in(Proposal::getCaseNumber, caseNumList));

        if (ObjectUtil.isEmpty(dataList)) {
            return;
        } else {
            String conflictCaseNum = dataList.stream()
                    .map(data -> String.valueOf(data.getCaseNumber()))
                    .collect(Collectors.joining(","));
            throw new ServiceException("案号" + conflictCaseNum + "已存在");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelCase(FileCaseDto fileCaseDto) {

        if (ObjectUtil.isEmpty(proposalMapper.selectById(fileCaseDto.getProposalId()))) {
            throw new ServiceException("该提案不存在");
        }

        if (proposalHandleMapper.existsByProposalId(fileCaseDto.getProposalId())) {
            throw new ServiceException("该提案已办理");
        }

        proposalMapper.changeCaseFiling(fileCaseDto.getProposalId(), CaseFillingEnum.WITHDRAW);
        proposalVerifyRecordService.addCancelCaseVerifyRecord(fileCaseDto);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignCase(FileCaseDto fileCaseDto) {

        if (ObjectUtil.isEmpty(proposalMapper.selectById(fileCaseDto.getProposalId()))) {
            throw new ServiceException("该提案不存在");
        }


        if (proposalHandleMapper.existsByProposalId(fileCaseDto.getProposalId())) {
            throw new ServiceException("该提案已办理");
        }

        ProposalHandle proposalHandle = new ProposalHandle();
        proposalHandle.setProposalId(fileCaseDto.getProposalId());
        proposalHandle.setUndertakeWay(fileCaseDto.getUndertakeWay().name());
        proposalHandle.setWillSendSms(fileCaseDto.getWillSendSms());
        if (ObjectUtil.isNotEmpty(fileCaseDto.getUndertakeUnits())) {
            String organizers = fileCaseDto.getUndertakeUnits()
                    .stream()
                    .map(UndertakeUnitDto::getUnitName)
                    .collect(Collectors.joining(","));
            proposalHandle.setOrganizers(organizers);
        }
        // 办理信息
        proposalHandleMapper.insert(proposalHandle);
        // 承办单位信息
        proposalUndertakeUnitService.insertUndertakeUnit(Long.valueOf(proposalHandle.getId()), fileCaseDto.getUndertakeUnits());
        // 添加接收情况
        proposalReceptionService.addReception(fileCaseDto.getProposalId(), fileCaseDto.getUndertakeUnits());
        // 添加审核记录
        proposalVerifyRecordService.addAssignCaseVerifyRecord(fileCaseDto);

        // 更新提案信息
        Proposal proposal = new Proposal();
        proposal.setId(fileCaseDto.getProposalId());
        proposal.setCaseFiling(CaseFillingEnum.WAIT_HANDLE);
        proposal.setIsAssigned(true);
        proposal.setIsOpen(fileCaseDto.getIsOpen());

        proposalMapper.updateById(proposal);

        return true;
    }

    @Override
    public FileCaseVo getFileCaseInfo(String proposalId) {
        FileCaseVo fileCaseVo = new FileCaseVo();

        Proposal proposal = proposalMapper.selectById(proposalId);
        if (ObjectUtil.isNotEmpty(proposal)) {
            fileCaseVo.setProposalId(proposalId);
            fileCaseVo.setIsOpen(proposal.getIsOpen());
        }

        ProposalHandle handleInfo = proposalHandleMapper.getProposalHandleByProposalId(proposalId);
        if (ObjectUtil.isNotEmpty(handleInfo)) {
            fileCaseVo.setProposalId(proposalId);
            fileCaseVo.setUndertakeWay(UndertakeWayEnum.valueOf(handleInfo.getUndertakeWay()));
            fileCaseVo.setWillSendSms(handleInfo.getWillSendSms());
            fileCaseVo.setUndertakeUnits(proposalUndertakeUnitService.getUndertakeUnitList(Long.valueOf(handleInfo.getId())));
        }

        return fileCaseVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeHandle(FileCaseDto fileCaseDto) {

        Proposal proposal = proposalMapper.selectById(fileCaseDto.getProposalId());
        if (ObjectUtil.isEmpty(proposal)) {
            throw new ServiceException("该提案不存在");
        }

        if (proposal.getCaseFiling().equals(CaseFillingEnum.FINISH)) {
            throw new ServiceException("该提案已办结");
        }

        ProposalHandle handleInfo = proposalHandleMapper.getProposalHandleByProposalId(fileCaseDto.getProposalId());
        if (ObjectUtil.isEmpty(handleInfo)) {
            throw new ServiceException("该提案未交办");
        }

        List<UndertakeUnitDto> undertakeUnits = fileCaseDto.getUndertakeUnits();
        if (ObjectUtil.isEmpty(undertakeUnits)) {
            throw new ServiceException("承办单位不能为空");
        }


        List<UndertakeUnitDto> updateUnits = undertakeUnits.stream().filter(unit -> ObjectUtil.isNotEmpty(unit.getId())).collect(Collectors.toList());
        // 需要删除的承办单位
        proposalUndertakeUnitService.deleteUndertakeUnit(Long.valueOf(handleInfo.getId()), updateUnits);

        // 更新的承办单位
        proposalUndertakeUnitService.editUndertakeUnit(updateUnits);

        // 新增的承办单位
        List<UndertakeUnitDto> newUnits = undertakeUnits.stream().filter(unit -> ObjectUtil.isEmpty(unit.getId())).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(newUnits)) {
            proposalUndertakeUnitService.insertUndertakeUnit(Long.valueOf(handleInfo.getId()), newUnits);
        }

        // 更新接收信息
        proposalReceptionService.saveOrUpdate(fileCaseDto.getProposalId(), fileCaseDto.getUndertakeUnits());

        String Organizers = undertakeUnits.stream().map(UndertakeUnitDto::getUnitName).collect(Collectors.joining(","));
        proposalHandleMapper.update(null, new LambdaUpdateWrapper<ProposalHandle>()
                .set(ProposalHandle::getOrganizers, Organizers)
                .eq(ProposalHandle::getId, handleInfo.getId())
        );

        return true;
    }


    @Override
    public String getProposer(String proposer) {
        if (!StringUtils.hasLength(proposer)) {
            return "";
        }
        String[] proposerList = proposer.split(",");
        StringBuilder result = new StringBuilder();
        if (proposerList.length > 0) {
            if (proposerList.length == 1) {
                result.append(proposerList[0], 0, proposerList[0].lastIndexOf("("));
            } else {
                result.append(proposerList[0], 0, proposerList[0].lastIndexOf("("))
                        .append("等")
                        .append(proposerList.length)
                        .append("人");
            }
        }
        return result.toString();
    }

    private String getProposer(List<ProposerVo> proposerList) {
        StringBuilder proposerStr = new StringBuilder();
        if (!CollectionUtils.isEmpty(proposerList)) {
            for (int index = 0; index < proposerList.size(); index++) {
                ProposerVo proposerVo = proposerList.get(index);
                if (index == proposerList.size() - 1) {
                    proposerStr.append(proposerVo.getProposer());
                    proposerStr.append("(").append(proposerVo.getSubmitType()).append(")");
                } else {
                    proposerStr.append(proposerVo.getProposer()).append("(").append(proposerVo.getSubmitType()).append("),");
                }

            }
        }

        return ObjectUtil.isEmpty(proposerStr) ? null : proposerStr.toString();
    }

    private String getOrganizer(List<String> organizerList) {
        String organizers = null;
        if (!CollectionUtils.isEmpty(organizerList)) {
            if (organizerList.size() == 1) {
                organizers = organizerList.get(0);
            } else {
                organizers = organizerList.get(0) + "等";
            }
        }

        return organizers;
    }


}
