package com.ruoyi.project.proposal.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.project.proposal.converter.ProposalHandleConverter;
import com.ruoyi.project.proposal.domain.ProposalUndertakeUnit;
import com.ruoyi.project.proposal.domain.dto.EvaluationExcelDto;
import com.ruoyi.project.proposal.domain.vo.*;
import com.ruoyi.project.proposal.mapper.ProposalHandleOrganizerMapper;
import com.ruoyi.project.proposal.mapper.ProposalUndertakeUnitMapper;
import com.ruoyi.project.proposal.service.IProposalService;
import org.springframework.stereotype.Service;
import com.ruoyi.project.proposal.mapper.ProposalHandleMapper;
import com.ruoyi.project.proposal.domain.ProposalHandle;
import com.ruoyi.project.proposal.service.IProposalHandleService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 办理信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Service
public class ProposalHandleServiceImpl implements IProposalHandleService {

    @Resource
    private IProposalService proposalService;

    @Resource
    private ProposalHandleMapper proposalHandleMapper;

    @Resource
    private ProposalHandleOrganizerMapper proposalHandleOrganizerMapper;

    @Resource
    private ProposalUndertakeUnitMapper proposalUndertakeUnitMapper;

    /**
     * 查询办理信息
     * 
     * @param id 办理信息主键
     * @return 办理信息
     */
    @Override
    public ProposalHandle selectProposalHandleById(Long id) {
        return proposalHandleMapper.selectById(id);
    }


    /**
     * 修改办理信息
     * 
     * @param proposalHandleEditVo 办理信息
     * @return 结果
     */
    @Override
    public int updateProposalHandle(ProposalHandleEditVo proposalHandleEditVo) {
        ProposalHandle proposalHandle = ProposalHandleConverter.INSTANCE.convert(proposalHandleEditVo);
        return proposalHandleMapper.updateById(proposalHandle);
    }


    @Override
    public IPage<ProposalHandleEvaPageVo> selectProposalHandleEvaPage(ProposalHandlePageParamVo pageParamVo) {

        Page<ProposalHandleEvaPageVo> page = new Page<>(pageParamVo.getCurrentPage(), pageParamVo.getPageSize());
        IPage<ProposalHandleEvaPageVo> handleIPage = proposalHandleMapper.selectProposalHandleEvaPage(page, pageParamVo);
        for (ProposalHandleEvaPageVo record : handleIPage.getRecords()) {
            String proposer = proposalService.getProposer(record.getProposer());
            record.setProposer(proposer);
            record.setOrganizers(getOrganizer(record.getOrganizerList()));
        }
        return handleIPage;
    }

    @Override
    public IPage<ProposalHandlePageVo> selectProposalHandleRevertPage(ProposalHandlePageParamVo pageParamVo) {

        Page<ProposalHandlePageVo> page = new Page<>(pageParamVo.getCurrentPage(), pageParamVo.getPageSize());
        IPage<ProposalHandlePageVo> handleIPage = proposalHandleMapper.selectProposalHandleRevertPage(page, pageParamVo);
        for (ProposalHandlePageVo record : handleIPage.getRecords()) {
            String proposer = proposalService.getProposer(record.getProposer());
            record.setProposer(proposer);
            record.setOrganizers(getOrganizer(record.getOrganizerList()));
        }
        return handleIPage;
    }

    @Override
    public IPage<ProposalHandlePageVo> selectHandleBackPage(ProposalHandlePageParamVo pageParamVo) {

        Long unitId = SecurityUtils.getUserId();

        List<ProposalUndertakeUnit> undertakeUnits = proposalUndertakeUnitMapper.selectByUnitId(unitId);
        if (ObjectUtil.isNotEmpty(undertakeUnits)) {
            Set<Long> ids = undertakeUnits.stream().map(ProposalUndertakeUnit::getHandleId).collect(Collectors.toSet());
            Page<ProposalHandlePageVo> page = new Page<>(pageParamVo.getCurrentPage(), pageParamVo.getPageSize());

            return proposalHandleMapper.selectProposalHandleBackPage(page, pageParamVo, ids);
        }

        return new Page<>();
    }

    @Override
    public IPage<ProposalHandlePageVo> selectProposalHandlePage(ProposalHandlePageParamVo pageParamVo) {
        // 当前用户部门id
        Long unitId = SecurityUtils.getUserId();
        Page<ProposalHandlePageVo> page = new Page<>(pageParamVo.getCurrentPage(), pageParamVo.getPageSize());
        return proposalHandleMapper.selectProposalHandlePage(page, pageParamVo, unitId);
    }

    @Override
    public int revertProposalHandle(Long handleId) {
        return proposalHandleMapper.revertProposalHandle(handleId);
    }

    @Override
    public List<EvaluationExcelVo> selectEvaluationExcelVoList() {
        List<EvaluationExcelDto> evaluationDtoList = proposalHandleMapper.selectEvaluationExcelDtoList();

        List<String> proposalIdList = evaluationDtoList.stream().map(EvaluationExcelDto::getProposalId).collect(Collectors.toList());
        List<String> handleIdList = evaluationDtoList.stream().map(EvaluationExcelDto::getHandleId).collect(Collectors.toList());

        Map<String, String> organizerMap = getOrganizerMap(handleIdList);
        Map<String, String> proposerMap = proposalService.getProposerMap(proposalIdList);

        List<EvaluationExcelVo> evaluationList = new ArrayList<>();


        for (EvaluationExcelDto evaluationExcelDto : evaluationDtoList) {
            EvaluationExcelVo evaluationExcelVo = ProposalHandleConverter.INSTANCE.convert(evaluationExcelDto);
            Class<?> clazz = evaluationExcelVo.getClass();
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                try {
                    if (field.get(evaluationExcelVo) != null) {
                        if (field.get(evaluationExcelVo).equals("1")) {
                            field.set(evaluationExcelVo, "是");
                        } else if (field.get(evaluationExcelVo).equals("0")) {
                            field.set(evaluationExcelVo, "否");
                        }
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }

            evaluationExcelVo.setOrganizer(organizerMap.get(evaluationExcelDto.getHandleId()));
            evaluationExcelVo.setProposer(proposerMap.get(evaluationExcelDto.getProposalId()));
            evaluationList.add(evaluationExcelVo);
        }

        return evaluationList;
    }

    @Override
    public Map<String, String> getOrganizerMap(List<String> handleIdList) {

        Map<String, String> organizerMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(handleIdList)) {
            List<Map<String, Object>> dataList = proposalHandleOrganizerMapper.selectOrganizerList(handleIdList);

            for (Map<String, Object> data : dataList) {
                String handleId = data.get("handle_id").toString();
                String deptName = data.get("dept_name").toString();

                if (organizerMap.containsKey(handleId)) {
                    String organizer = organizerMap.get(handleId);
                    organizerMap.put(handleId, organizer + "," + deptName);
                } else {
                    organizerMap.put(handleId, deptName);
                }
            }
        }



        return organizerMap;
    }

    private String getOrganizer(List<String> organizerList) {
        String organizers = null;
        if (!CollectionUtils.isEmpty(organizerList)) {
            if (organizerList.size() == 1) {
                organizers = organizerList.get(0);
            } else {
                organizers = organizerList.get(0) + "等";
            }
        }

        return organizers;
    }
}
