package com.ruoyi.project.proposal.controller;


import javax.servlet.http.HttpServletResponse;

import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.proposal.domain.ProposalHandleEvaluations;
import com.ruoyi.project.proposal.service.IProposalHandleEvaluationsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 提案办理情况评价Controller
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
@RestController
@RequestMapping("/handle/evaluations")
public class ProposalHandleEvaluationsController extends BaseController {

    @Autowired
    private IProposalHandleEvaluationsService handleEvaluationsService;



    /**
     * 获取提案办理情况评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:evaluations:query')")
    @GetMapping(value = "/getEvaluation")
    public AjaxResult getInfo(@RequestParam("id") String handleId) {
        return AjaxResult.success(handleEvaluationsService.getHandleEvaluationsById(handleId));
    }

    /**
     * 新增提案办理情况评价
     */
    @PreAuthorize("@ss.hasPermi('system:evaluations:add')")
    @Log(title = "提案办理情况评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProposalHandleEvaluations handleEvaluations) {
        return AjaxResult.success(handleEvaluationsService.addHandleEvaluation(handleEvaluations));
    }


}
