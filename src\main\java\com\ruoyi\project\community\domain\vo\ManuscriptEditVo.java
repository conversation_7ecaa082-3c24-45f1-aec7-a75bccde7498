package com.ruoyi.project.community.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.manuscript.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ManuscriptEditVo {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("稿件编号")
    private String code;

    @ApiModelProperty("签发期数")
    private String issueNumber;

    @ApiModelProperty("主送单位")
    private String recipient;

    @ApiModelProperty("报送单位")
    private List<ReportUnitEnum> reportUnit;

    @ApiModelProperty("报送单位明细")
    private ReportUnitVo reportUnitDetail;

    @ApiModelProperty("报送对象")
    private List<ReportTargetEnum> reportTarget;

    @ApiModelProperty("其他报送对象")
    private String otherReportTarget;

    @ApiModelProperty("采用方式")
    private AdoptWayEnum adoptWay;

    @ApiModelProperty("报送类别")
    private ReportTypeEnum reportType;

    @ApiModelProperty("稿件标题")
    private String title;

    @ApiModelProperty("稿件正文")
    private String content;

    @ApiModelProperty("稿件类型")
    private CategoryEnum category;

    @ApiModelProperty("类型明细")
    private CategoryDetailEnum categoryDetail;

    @ApiModelProperty("来稿方式")
    private String submitWay;

    @ApiModelProperty("来稿时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submissionTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("稿件状态")
    private ManuscriptStatusEnum status;

    @ApiModelProperty("反映人")
    private List<String> reflectorList;

    @ApiModelProperty("信息文稿")
    private List<String> fileList;
}
