# SQL日志优化解决方案

## 🚨 问题分析

`calculateScore` 方法会产生大量SQL日志的原因：
- 每个委员 × 每个规则 × 每个策略方法 = 大量Mapper调用
- 192个委员 × 多个规则 × 多个策略 = 可能数千次SQL查询
- 每次查询都会产生SQL日志输出

## 🎯 解决方案

### **方案1：日志级别控制（推荐 - 最简单）**

#### **1.1 调整MyBatis日志级别**
```yaml
# application.yml
logging:
  level:
    # 将策略Mapper的日志级别调整为WARN，减少SQL日志
    com.ruoyi.project.committee.evalrule.mapper.BasicStrategyMapper: WARN
    com.ruoyi.project.committee.evalrule.mapper.RewardStrategyMapper: WARN
    
    # 或者更精确地控制
    com.ruoyi.project.committee.evalrule: WARN
    
    # 保留其他重要日志
    com.ruoyi.project.committee.evalrule.service: INFO
```

#### **1.2 动态日志级别控制**
```java
@Service
public class RuleScoreServiceImpl {
    
    @Transactional(rollbackFor = Exception.class)
    public Integer calculateScore(Integer year) {
        // 临时调整日志级别
        Logger basicMapperLogger = LoggerFactory.getLogger("com.ruoyi.project.committee.evalrule.mapper.BasicStrategyMapper");
        Logger rewardMapperLogger = LoggerFactory.getLogger("com.ruoyi.project.committee.evalrule.mapper.RewardStrategyMapper");
        
        Level originalBasicLevel = ((ch.qos.logback.classic.Logger) basicMapperLogger).getLevel();
        Level originalRewardLevel = ((ch.qos.logback.classic.Logger) rewardMapperLogger).getLevel();
        
        try {
            // 设置为WARN级别，减少SQL日志
            ((ch.qos.logback.classic.Logger) basicMapperLogger).setLevel(Level.WARN);
            ((ch.qos.logback.classic.Logger) rewardMapperLogger).setLevel(Level.WARN);
            
            // 执行计算逻辑
            // ... 原有代码
            
        } finally {
            // 恢复原始日志级别
            ((ch.qos.logback.classic.Logger) basicMapperLogger).setLevel(originalBasicLevel);
            ((ch.qos.logback.classic.Logger) rewardMapperLogger).setLevel(originalRewardLevel);
        }
    }
}
```

### **方案2：批量查询优化（推荐 - 最有效）**

#### **2.1 预加载数据**
```java
private List<RuleScore> calculateScore(List<CommitteeMember> memberList, RuleInfoVo ruleInfo,
                                       TripleMap<String, String, String> strategyMap) {
    List<RuleScore> ruleScoreList = new ArrayList<>();
    
    // 预加载所有委员的数据，减少SQL查询次数
    Map<String, Map<String, Integer>> preloadedData = preloadAllMemberData(memberList, strategyMap);
    
    for (CommitteeMember member : memberList) {
        ScoreAccumulator accumulator = new ScoreAccumulator();
        
        for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
            resetFinalScores(ruleDetailVo);
            // 使用预加载的数据，避免重复查询
            evaluateLeafNodesWithCache(member, ruleDetailVo, strategyMap, preloadedData);
            accumulateScoresByStrategyType(ruleDetailVo, strategyMap, accumulator);
        }
        
        // 构建RuleScore对象...
    }
    
    return ruleScoreList;
}

/**
 * 预加载所有委员的数据
 */
private Map<String, Map<String, Integer>> preloadAllMemberData(List<CommitteeMember> memberList, 
                                                               TripleMap<String, String, String> strategyMap) {
    Map<String, Map<String, Integer>> result = new HashMap<>();
    
    // 收集所有需要查询的策略方法
    Set<String> allMethods = new HashSet<>();
    strategyMap.values().forEach(pair -> allMethods.add(pair.getValue()));
    
    // 批量查询每种类型的数据
    for (String methodName : allMethods) {
        Map<String, Integer> methodData = batchQueryByMethod(memberList, methodName);
        for (CommitteeMember member : memberList) {
            result.computeIfAbsent(member.getId().toString(), k -> new HashMap<>())
                  .put(methodName, methodData.getOrDefault(member.getId().toString(), 0));
        }
    }
    
    return result;
}
```

#### **2.2 批量查询Mapper方法**
```java
// 在BasicStrategyMapper和RewardStrategyMapper中添加批量查询方法
public interface BasicStrategyMapper {
    
    // 原有的单个查询方法
    Integer handleMeetingAttendance(CommitteeMember member);
    
    // 新增的批量查询方法
    Map<String, Integer> batchHandleMeetingAttendance(@Param("memberList") List<CommitteeMember> memberList);
    
    // 通用批量查询方法
    Map<String, Integer> batchQueryByMethod(@Param("memberList") List<CommitteeMember> memberList, 
                                           @Param("methodName") String methodName);
}
```

### **方案3：缓存优化**

#### **3.1 本地缓存**
```java
@Service
public class RuleScoreServiceImpl {
    
    // 使用Caffeine缓存
    private final Cache<String, Integer> scoreCache = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();
    
    private Integer calculateScoreByStrategyWithCache(CommitteeMember member, RuleDetailVo ruleDetail,
                                                     Pair<String, String> strategyPair) throws Exception {
        String cacheKey = member.getId() + ":" + strategyPair.getValue() + ":" + member.getYear();
        
        return scoreCache.get(cacheKey, key -> {
            try {
                return calculateScoreByStrategy(member, ruleDetail, strategyPair);
            } catch (Exception e) {
                log.error("计算分数失败: {}", e.getMessage());
                return 0;
            }
        });
    }
}
```

#### **3.2 Redis缓存**
```java
@Service
public class RuleScoreServiceImpl {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private Integer calculateScoreByStrategyWithRedis(CommitteeMember member, RuleDetailVo ruleDetail,
                                                     Pair<String, String> strategyPair) throws Exception {
        String cacheKey = "score:" + member.getYear() + ":" + member.getId() + ":" + strategyPair.getValue();
        
        Integer cachedScore = (Integer) redisTemplate.opsForValue().get(cacheKey);
        if (cachedScore != null) {
            return cachedScore;
        }
        
        Integer score = calculateScoreByStrategy(member, ruleDetail, strategyPair);
        redisTemplate.opsForValue().set(cacheKey, score, Duration.ofHours(1));
        
        return score;
    }
}
```

### **方案4：异步处理**

#### **4.1 分批异步计算**
```java
@Service
public class RuleScoreServiceImpl {
    
    @Async("taskExecutor")
    public CompletableFuture<List<RuleScore>> calculateScoreAsync(List<CommitteeMember> memberList, 
                                                                 RuleInfoVo ruleInfo,
                                                                 TripleMap<String, String, String> strategyMap) {
        // 分批处理，每批50个委员
        int batchSize = 50;
        List<CompletableFuture<List<RuleScore>>> futures = new ArrayList<>();
        
        for (int i = 0; i < memberList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, memberList.size());
            List<CommitteeMember> batch = memberList.subList(i, end);
            
            CompletableFuture<List<RuleScore>> future = CompletableFuture.supplyAsync(() -> 
                calculateScoreBatch(batch, ruleInfo, strategyMap));
            futures.add(future);
        }
        
        // 合并所有结果
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(List::stream)
                        .collect(Collectors.toList()));
    }
}
```

## 📊 方案对比

| 方案 | 实现难度 | 性能提升 | 日志减少 | 推荐指数 |
|------|----------|----------|----------|----------|
| 日志级别控制 | ⭐ | - | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 批量查询优化 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 本地缓存 | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Redis缓存 | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| 异步处理 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐ |

## 🎯 推荐实施步骤

### **第一步：立即实施（日志级别控制）**
```yaml
# application.yml - 立即生效
logging:
  level:
    com.ruoyi.project.committee.evalrule.mapper: WARN
    com.ruoyi.project.committee.evalrule.service: INFO
```

### **第二步：中期优化（批量查询）**
1. 在Mapper中添加批量查询方法
2. 实现预加载数据逻辑
3. 修改策略执行逻辑使用缓存数据

### **第三步：长期优化（缓存策略）**
1. 引入本地缓存（Caffeine）
2. 实现缓存键策略
3. 设置合理的缓存过期时间

## ⚠️ 注意事项

### **日志级别控制**
- 不影响功能，只是减少日志输出
- 可能影响问题排查，建议保留ERROR和WARN级别

### **批量查询优化**
- 需要修改Mapper和SQL，工作量较大
- 内存使用会增加，需要注意内存管理
- 对于大数据量需要分批处理

### **缓存策略**
- 需要考虑数据一致性问题
- 缓存失效策略要合理
- 内存使用需要监控

## 总结

**立即可用的方案**：调整日志级别，快速减少SQL日志输出
**最佳长期方案**：批量查询 + 本地缓存，既提升性能又减少日志
**渐进式实施**：先控制日志，再逐步优化查询和缓存策略
