package com.ruoyi.project.proposal.controller;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.proposal.domain.vo.*;
import com.ruoyi.project.proposal.utils.ExcelHeaderGenerator;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.project.proposal.service.IProposalHandleService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;


/**
 * 办理信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/proposal/handle")
public class ProposalHandleController extends BaseController {

    @Autowired
    private IProposalHandleService proposalHandleService;

    /**
     * 获取办理评价分页信息
     * @param pageParamVo 分页查询参数
     * @return ProposalHandleEvaPageVo
     */
    @PreAuthorize("@ss.hasPermi('system:handle:list')")
    @PostMapping("/evaPage")
    public TableDataInfo getEvaPageInfo (@RequestBody ProposalHandlePageParamVo pageParamVo) {
        IPage<ProposalHandleEvaPageVo> evaPageVoIPage = proposalHandleService.selectProposalHandleEvaPage(pageParamVo);
        return getDataTable(evaPageVoIPage.getRecords(), evaPageVoIPage.getTotal());
    }

    /**
     * 获取办理退回分页信息
     * @param pageParamVo 分页查询参数
     * @return ProposalHandleEvaPageVo
     */
    @PreAuthorize("@ss.hasPermi('system:handle:list')")
    @PostMapping("/revertPage")
    public TableDataInfo getRevertPageInfo (@RequestBody ProposalHandlePageParamVo pageParamVo) {
        IPage<ProposalHandlePageVo> revertPageVoIPage = proposalHandleService.selectProposalHandleRevertPage(pageParamVo);
        return getDataTable(revertPageVoIPage.getRecords(), revertPageVoIPage.getTotal());
    }

    /**
     * 办理退回分页（承办单位）
     * @param pageParamVo 分页查询参数
     * @return result
     */
    @PreAuthorize("@ss.hasPermi('handleBack:unit:list')")
    @PostMapping("/getHandleBackPage")
    public TableDataInfo getHandleBackPage(@RequestBody ProposalHandlePageParamVo pageParamVo) {
        IPage<ProposalHandlePageVo> pageResult = proposalHandleService.selectHandleBackPage(pageParamVo);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 提案办理分页（承办单位）
     * @param pageParamVo 分页查询参数
     * @return result
     */
    @PreAuthorize("@ss.hasPermi('handle:unit:list')")
    @PostMapping("/getHandlePage")
    public TableDataInfo getHandlePage(@RequestBody ProposalHandlePageParamVo pageParamVo) {
        IPage<ProposalHandlePageVo> pageResult = proposalHandleService.selectProposalHandlePage(pageParamVo);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }


    /**
     * 获取办理信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:handle:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(proposalHandleService.selectProposalHandleById(id));
    }


    /**
     * 修改办理信息
     */
    @PreAuthorize("@ss.hasPermi('system:handle:edit')")
    @Log(title = "办理信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProposalHandleEditVo proposalHandleEditVo) {
        return toAjax(proposalHandleService.updateProposalHandle(proposalHandleEditVo));
    }

    /**
     * 办理退回
     */
    @PreAuthorize("@ss.hasPermi('system:handle:edit')")
    @Log(title = "办理信息", businessType = BusinessType.UPDATE)
    @GetMapping("/revert")
    public AjaxResult revertProposalHandle(@RequestParam("handleId") Long handleId) {
        return toAjax(proposalHandleService.revertProposalHandle(handleId));
    }


    @PreAuthorize("@ss.hasPermi('system:handle:export')")
    @Log(title = "办理信息", businessType = BusinessType.UPDATE)
    @GetMapping("/export")
    public void exportHandleEvaluation(HttpServletResponse response) {

        List<EvaluationExcelVo> evaluationList = proposalHandleService.selectEvaluationExcelVoList();

        Class<?> clazz = EvaluationExcelVo.class;
        List<List<String>> headRows = ExcelHeaderGenerator.generateHeader(clazz);

        ExcelWriter writer = ExcelUtil.getWriter(true);


        ExcelHeaderGenerator.setHeadersStyle(headRows, writer);



        // 写入数据
        writer.write(evaluationList);




        try {
            String fileName = new String("办理情况".getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            writer.flush(response.getOutputStream());
        } catch (IOException ioe) {
            ioe.printStackTrace();
        } finally {
            writer.close();
        }

    }


}
