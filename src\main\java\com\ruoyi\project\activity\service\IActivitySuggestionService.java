package com.ruoyi.project.activity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.activity.domain.ActivitySuggestion;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionUpdateDTO;
import com.ruoyi.project.activity.domain.vo.ActivitySuggestionDetailVO;
import com.ruoyi.project.activity.domain.vo.ActivitySuggestionVO;

/**
 * 意见建议服务接口
 */
public interface IActivitySuggestionService extends IService<ActivitySuggestion> {

    /**
     * 新增意见建议
     *
     * @param dto 新增意见建议DTO
     * @return 是否新增成功
     */
    boolean addActivitySuggestion(ActivitySuggestionAddDTO dto);

    /**
     * 修改意见建议
     *
     * @param dto 修改意见建议DTO
     * @return 是否修改成功
     */
    boolean updateActivitySuggestion(ActivitySuggestionUpdateDTO dto);

    /**
     * 分页查询意见建议
     *
     * @param pageDTO 分页查询参数
     * @return 分页查询结果
     */
    IPage<ActivitySuggestionVO> pageActivitySuggestion(ActivitySuggestionPageDTO pageDTO);

    /**
     * 获取意见建议详情
     *
     * @param id 意见建议ID
     * @return 意见建议详情
     */
    ActivitySuggestionDetailVO getActivitySuggestionDetail(Long id);
}
