package com.ruoyi.project.committee.resumption.service.impl;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.AuditStatusEnum;
import com.ruoyi.common.enums.committee.PersonTypeEnum;
import com.ruoyi.common.enums.committee.ReportTypeEnum;
import com.ruoyi.common.enums.honor.StatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.project.committee.resumption.converter.PriorityConverter;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityAuditDto;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityEditDto;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityPageDto;
import com.ruoyi.project.committee.resumption.domain.po.ExamineParticipant;
import com.ruoyi.project.committee.resumption.domain.po.Priority;
import com.ruoyi.project.committee.resumption.domain.vo.ParticipantVo;
import com.ruoyi.project.committee.resumption.domain.vo.PriorityPageVo;
import com.ruoyi.project.committee.resumption.domain.vo.PriorityVo;
import com.ruoyi.project.committee.resumption.mapper.PriorityMapper;
import com.ruoyi.project.committee.resumption.service.IPriorityService;
import com.ruoyi.project.proposal.domain.Annex;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.proposal.mapper.AnnexMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 重点工作信息 服务实现类
 */
@Service
public class PriorityServiceImpl extends ServiceImpl<PriorityMapper, Priority> implements IPriorityService {

    @Resource
    private PriorityMapper priorityMapper;

    @Resource
    private ExamineParticipantService examineParticipantService;

    @Resource
    private AnnexMapper annexMapper;

    private void validate(Long id) {
        if (priorityMapper.selectById(id) == null) {
            throw new ServiceException("该重点工作信息不存在");
        }
    }

    /**
     * 查询重点工作信息列表
     *
     * @param pageDto 重点工作信息查询参数
     * @return 重点工作信息列表
     */
    @Override
    public IPage<PriorityPageVo> selectPriorityPage(PriorityPageDto pageDto) {

        Set<String> priorityIds = null;
        if (ObjectUtil.isNotEmpty(pageDto.getPeriod()) || ObjectUtil.isNotEmpty(pageDto.getRate())
                || ObjectUtil.isNotEmpty(pageDto.getBelongSpecialCommittee()) || ObjectUtil.isNotEmpty(pageDto.getParticipant())) {
            priorityIds = examineParticipantService.getBaseMapper().selectReportPkIdByCommittee(pageDto.getParticipant(),
                    pageDto.getPeriod(), pageDto.getRate(), null, pageDto.getBelongSpecialCommittee());
            if (ObjectUtil.isEmpty(priorityIds)) {
                return PageUtils.emptyPage();
            }
        }

        LambdaQueryWrapper<Priority> queryWrapper = new LambdaQueryWrapper<Priority>()
                .like(ObjectUtil.isNotEmpty(pageDto.getTitle()), Priority::getTitle, pageDto.getTitle())
                .eq(ObjectUtil.isNotEmpty(pageDto.getAuditStatus()), Priority::getAuditStatus, pageDto.getAuditStatus())
                .ge(ObjectUtil.isNotEmpty(pageDto.getStartTime()), Priority::getCollectTime, pageDto.getStartTime())
                .le(ObjectUtil.isNotEmpty(pageDto.getEndTime()), Priority::getCollectTime, pageDto.getEndTime())
                .in(ObjectUtil.isNotEmpty(priorityIds), Priority::getId, priorityIds)
                .orderByDesc(Priority::getCreateTime);

        if (SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRoles())) {
            queryWrapper.eq(Priority::getStatus, StatusEnum.SUBMITTED.getCode());
        }


        Page<Priority> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        IPage<Priority> pageResult = priorityMapper.selectPage(page, queryWrapper);

        IPage<PriorityPageVo> convertResult = PriorityConverter.INSTANCE.convertToPage(pageResult);

        if (ObjectUtil.isNotEmpty(convertResult.getRecords())) {
            List<String> ids = convertResult.getRecords().stream().map(PriorityPageVo::getId).collect(Collectors.toList());
            List<ParticipantVo> participantList = examineParticipantService.getBaseMapper().selectParticipant(ids);

            Map<String, String> participantMap = participantList.stream()
                    .collect(Collectors.groupingBy(
                            ParticipantVo::getPkId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> list.stream()
                                            .map(ParticipantVo::getCommitteeName)
                                            .filter(Objects::nonNull)
                                            .collect(Collectors.joining(","))
                            )
                    ));

            convertResult.getRecords().forEach(priorityPageVo -> {
                priorityPageVo.setParticipant(participantMap.get(priorityPageVo.getId()));
            });
        }

        return convertResult;
    }

    @Override
    public IPage<PriorityPageVo> selectMyPriorityPage(PriorityPageDto pageDto) {
        Long userId = SecurityUtils.getUserId();
        Set<String> reportIdList = examineParticipantService.getBaseMapper()
                .selectReportPkIdByCommittee(null, null, null, String.valueOf(userId), null);

        if (ObjectUtil.isEmpty(reportIdList)) {
            return PageUtils.emptyPage();
        }

        Page<Priority> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        IPage<Priority> pageResult = priorityMapper.selectPage(page, new LambdaQueryWrapper<Priority>()
                .like(ObjectUtil.isNotEmpty(pageDto.getTitle()), Priority::getTitle, pageDto.getTitle())
                .eq(ObjectUtil.isNotEmpty(pageDto.getAuditStatus()), Priority::getAuditStatus, pageDto.getAuditStatus())
                .eq(ObjectUtil.isNotEmpty(pageDto.getStatus()), Priority::getStatus, pageDto.getStatus())
                .apply(ObjectUtil.isNotEmpty(pageDto.getYear()),  "YEAR(create_time) = {0}", pageDto.getYear())
                .in(Priority::getId, reportIdList)
                .orderByDesc(Priority::getCreateTime)
        );

        IPage<PriorityPageVo> convertResult = PriorityConverter.INSTANCE.convertToPage(pageResult);
        if (ObjectUtil.isNotEmpty(convertResult.getRecords())) {
            String username = SecurityUtils.getUsername();
            convertResult.getRecords().forEach(item -> {
                item.setParticipant(username);
            });
        }

        return convertResult;
    }

    /**
     * 根据ID查询重点工作信息
     *
     * @param id 重点工作信息ID
     * @return 重点工作信息
     */
    @Override
    public PriorityVo selectPriorityById(String id) {
        Priority priority = priorityMapper.selectById(id);
        PriorityVo convertResult = PriorityConverter.INSTANCE.entityToVo(priority);

        if (ObjectUtil.isNotEmpty(convertResult)) {
            List<ParticipantVo> resultList = examineParticipantService.getBaseMapper().selectParticipantByReportPkId(id);

            Map<PersonTypeEnum, List<ParticipantVo>> groupMap = resultList.stream()
                    .collect(Collectors.groupingBy(ParticipantVo::getPersonType));
            convertResult.setGoodList(groupMap.getOrDefault(PersonTypeEnum.GOOD, Collections.emptyList()));
            convertResult.setOutstandingList(groupMap.getOrDefault(PersonTypeEnum.OUTSTANDING, Collections.emptyList()));
            convertResult.setParticipantList(groupMap.getOrDefault(PersonTypeEnum.PARTICIPANT, Collections.emptyList()));

            List<Long> annexIdList = JSON.parseArray(priority.getAnnex(), Long.class);
            if (ObjectUtil.isNotEmpty(annexIdList)) {
                List<Annex> annexes = annexMapper.selectBatchIds(annexIdList);
                List<AnnexVo> annexVoList = annexes.stream()
                        .map(annex -> {
                            AnnexVo annexVo = new AnnexVo();
                            BeanUtils.copyProperties(annex, annexVo);
                            return annexVo;
                        })
                        .collect(Collectors.toList());
                convertResult.setAnnexList(annexVoList);
            }
        }

        return convertResult;
    }

    /**
     * 新增重点工作信息
     *
     * @param submitDto 重点工作信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitPriority(PriorityEditDto submitDto) {

        if (ObjectUtil.isNotEmpty(submitDto.getId())) {
            updatePriority(submitDto);
            return Long.valueOf(submitDto.getId());
        } else {
            Priority priority = PriorityConverter.INSTANCE.convert(submitDto);
            priority.setId(null);
            priority.setAuditStatus(AuditStatusEnum.UNREVIEWED.getCode());
            if (SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getRoles())) {
                priority.setStatus(StatusEnum.SUBMITTED.getCode());
            }

            if (ObjectUtil.isNotEmpty(submitDto.getAnnexList())) {
                priority.setAnnex(JSON.toJSONString(submitDto.getAnnexList()));
            } else {
                priority.setAnnex(JSON.toJSONString(Collections.emptyList()));
            }

            priorityMapper.insert(priority);

            // 参与者
            examineParticipantService.saveBatch(createPriorityParticipants(String.valueOf(priority.getId()), submitDto));
            return priority.getId();
        }

    }

    /**
     * 修改重点工作信息
     *
     * @param editDto 重点工作信息
     * @return 结果
     */
    @Override
    public boolean updatePriority(PriorityEditDto editDto) {
        validate(Long.valueOf(editDto.getId()));
        Priority updateObj = PriorityConverter.INSTANCE.convert(editDto);
        if (ObjectUtil.isNotEmpty(editDto.getAnnexList())) {
            updateObj.setAnnex(JSON.toJSONString(editDto.getAnnexList()));
        } else {
            updateObj.setAnnex(JSON.toJSONString(Collections.emptyList()));
        }

        // 参与者
        List<ExamineParticipant> resetList = createPriorityParticipants(String.valueOf(updateObj.getId()), editDto);
        examineParticipantService.resetParticipant(String.valueOf(updateObj.getId()), resetList);
        return priorityMapper.updateById(updateObj) > 0;
    }

    /**
     * 批量删除重点工作信息
     *
     * @param idList 需要删除的重点工作信息ID数组
     * @return 结果
     */
    @Override
    public boolean deletePriorityByIds(List<String> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return true;
        }
        return priorityMapper.deleteBatchIds(idList) > 0;
    }


    /**
     * 审核重点工作信息
     *
     * @param auditDto 重点工作信息
     * @return 结果
     */
    @Override
    public boolean auditPriority(PriorityAuditDto auditDto) {
        priorityMapper.auditPriority(auditDto);
        return true;
    }

    @Override
    public boolean returnPriority(PriorityAuditDto auditDto) {
        if (ObjectUtil.isEmpty(auditDto.getIdList())) {
            throw new ServiceException("请选择要退回的记录");
        }
        priorityMapper.returnPriority(auditDto);
        return false;
    }

    private List<ExamineParticipant> createPriorityParticipants(String priorityId, PriorityEditDto priority) {

        List<ExamineParticipant> participantList = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(priority.getParticipantList())) {
            priority.getParticipantList().forEach(participant -> {
                ExamineParticipant examineParticipant = new ExamineParticipant();
                examineParticipant.setReportPkid(priorityId);
                examineParticipant.setCommitteePkid(participant);
                examineParticipant.setReportType(ReportTypeEnum.KEY_WORK);
                examineParticipant.setPersonType(PersonTypeEnum.PARTICIPANT);
                participantList.add(examineParticipant);
            });
        }

        if (ObjectUtil.isNotEmpty(priority.getGoodList())) {
            priority.getGoodList().forEach(good -> {
                ExamineParticipant examineParticipant = new ExamineParticipant();
                examineParticipant.setReportPkid(priorityId);
                examineParticipant.setCommitteePkid(good);
                examineParticipant.setReportType(ReportTypeEnum.KEY_WORK);
                examineParticipant.setPersonType(PersonTypeEnum.GOOD);
                participantList.add(examineParticipant);
            });
        }

        if (ObjectUtil.isNotEmpty(priority.getOutstandingList())) {
            priority.getOutstandingList().forEach(outstanding -> {
                ExamineParticipant examineParticipant = new ExamineParticipant();
                examineParticipant.setReportPkid(priorityId);
                examineParticipant.setCommitteePkid(outstanding);
                examineParticipant.setReportType(ReportTypeEnum.KEY_WORK);
                examineParticipant.setPersonType(PersonTypeEnum.OUTSTANDING);
                participantList.add(examineParticipant);
            });
        }

        return participantList;
    }
}
