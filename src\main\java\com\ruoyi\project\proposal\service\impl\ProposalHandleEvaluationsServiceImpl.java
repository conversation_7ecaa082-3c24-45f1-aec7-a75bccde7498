package com.ruoyi.project.proposal.service.impl;


import com.ruoyi.common.annotation.Category;
import com.ruoyi.common.enums.proposal.EvaCategoryEnum;
import com.ruoyi.project.proposal.domain.ProposalHandleEvaluations;
import com.ruoyi.project.proposal.mapper.ProposalHandleEvaluationsMapper;
import com.ruoyi.project.proposal.mapper.ProposalHandleMapper;
import com.ruoyi.project.proposal.service.IProposalHandleEvaluationsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;


/**
 * 提案办理情况评价Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class ProposalHandleEvaluationsServiceImpl implements IProposalHandleEvaluationsService {

    @Resource
    private ProposalHandleEvaluationsMapper handleEvaluationsMapper;

    @Resource
    private ProposalHandleMapper proposalHandleMapper;

    @Override
    public Map<String, Map<String, Object>> getHandleEvaluationsById(String handleId) {
        ProposalHandleEvaluations handleEvaluations = handleEvaluationsMapper.selectHandleEvaluationsById(handleId);
        Map<String, Map<String, Object>> categorizedFields = new HashMap<>();
        if (handleEvaluations != null) {

            try {
                Class<? extends ProposalHandleEvaluations> aClass = handleEvaluations.getClass();
                for (Field field : aClass.getDeclaredFields()) {
                    if (field.isAnnotationPresent(Category.class)) {
                        Category annotation = field.getAnnotation(Category.class);
                        EvaCategoryEnum category = annotation.category();

                        String description = annotation.description();

                        field.setAccessible(true);
                        // 如果尚未创建该类别，则创建一个新的映射
                        categorizedFields.computeIfAbsent(category.getDescription(), k -> new HashMap<>());

                        // 存储字段及其描述
                        categorizedFields.get(category.getDescription()).put(description, field.get(handleEvaluations));
                    }
                }
            } catch (IllegalAccessException e) {

                // TODO: 未完待续
                throw new RuntimeException(e);
            }

        }
        return categorizedFields;
    }

    @Override
    public int addHandleEvaluation(ProposalHandleEvaluations handleEvaluations) {
        int result = handleEvaluationsMapper.insert(handleEvaluations);
        proposalHandleMapper.updateHandleEvaluationStatus(Long.valueOf(handleEvaluations.getHandleId()));
        return result;
    }
}
