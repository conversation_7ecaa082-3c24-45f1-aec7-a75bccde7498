package com.ruoyi.project.community.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.project.community.domain.ManuscriptAnnex;
import com.ruoyi.project.community.mapper.ManuscriptAnnexMapper;
import com.ruoyi.project.community.service.IManuscriptAnnexService;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ManuscriptAnnexServiceImpl implements IManuscriptAnnexService {

    @Resource
    private ManuscriptAnnexMapper manuscriptAnnexMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchManuscriptAnnex(String manuscriptId, List<String> annexIdList) {

        if (ObjectUtil.isEmpty(annexIdList)) {
            return;
        }

        annexIdList.forEach(annexId -> {
            ManuscriptAnnex manuscriptAnnex = new ManuscriptAnnex();
            manuscriptAnnex.setManuscriptId(manuscriptId);
            manuscriptAnnex.setAnnexId(annexId);
            manuscriptAnnexMapper.insert(manuscriptAnnex);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateManuscriptAnnex(String manuscriptId, List<String> annexIdList) {
        manuscriptAnnexMapper.deleteByManuscriptId(manuscriptId);

        saveBatchManuscriptAnnex(manuscriptId, annexIdList);
    }

    @Override
    public List<AnnexVo> getAnnexList(String manuscriptId) {
        return manuscriptAnnexMapper.getAnnexList(manuscriptId);
    }
}
