package com.ruoyi.project.proposal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.proposal.domain.ProposalHandle;
import com.ruoyi.project.proposal.domain.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 办理信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
public interface IProposalHandleService {
    /**
     * 查询办理信息
     * 
     * @param id 办理信息主键
     * @return 办理信息
     */
    public ProposalHandle selectProposalHandleById(Long id);


    /**
     * 修改办理信息
     * 
     * @param proposalHandleEditVo 办理信息
     * @return 结果
     */
    public int updateProposalHandle(ProposalHandleEditVo proposalHandleEditVo);

    /**
     * 办理评价
     * 获取办理评价分页
     * @param pageParamVo 分页查询参数
     * @return 提案办理信息分页数据
     */
    IPage<ProposalHandleEvaPageVo> selectProposalHandleEvaPage(ProposalHandlePageParamVo pageParamVo);

    /**
     * 办理退回
     * 获取办理退回分页
     * @param pageParamVo 分页查询参数
     * @return
     */
    IPage<ProposalHandlePageVo> selectProposalHandleRevertPage(ProposalHandlePageParamVo pageParamVo);

    /**
     * 办理退回分页（承办单位）
     * @param pageParamVo 查询条件
     * @return result
     */
    IPage<ProposalHandlePageVo> selectHandleBackPage(ProposalHandlePageParamVo pageParamVo);

    /**
     * 获取办理分页（承办单位）
     * @param pageParamVo 查询条件
     * @return result
     */
    IPage<ProposalHandlePageVo> selectProposalHandlePage(ProposalHandlePageParamVo pageParamVo);

    /**
     * 提案办理退回
     * @param handleId 办理信息id
     * @return 结果
     */
    int revertProposalHandle(Long handleId);


    List<EvaluationExcelVo> selectEvaluationExcelVoList();


    Map<String, String> getOrganizerMap(List<String> handleIdList);

}
