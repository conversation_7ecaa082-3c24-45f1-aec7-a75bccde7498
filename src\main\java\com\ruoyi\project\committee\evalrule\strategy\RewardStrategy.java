package com.ruoyi.project.committee.evalrule.strategy;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.mapper.RewardStrategyMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class RewardStrategy {

    private final RewardStrategyMapper rewardStrategyMapper;

    // 预加载的数据缓存
    private final Map<String, Map<Long, Integer>> preloadedDataCache = new HashMap<>();
    private String currentYear;

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（特别突出）
     */
    public void handleEnvProtectionSupervisionOutstanding(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleEnvProtectionSupervisionOutstanding", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（表现较好）
     */
    public void handleEnvProtectionSupervisionGood(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleEnvProtectionSupervisionGood", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（有所参与）
     */
    public void handleEnvProtectionSupervisionParticipated(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleEnvProtectionSupervisionParticipated(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加脱贫攻坚与乡村振兴工作（特别突出）
     */
    public void handleRuralRevitalizationOutstanding(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleRuralRevitalizationOutstanding(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加脱贫攻坚与乡村振兴工作（表现较好）
     */
    public void handleRuralRevitalizationGood(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleRuralRevitalizationGood(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加脱贫攻坚与乡村振兴工作（有所参与）
     */
    public void handleRuralRevitalizationParticipated(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleRuralRevitalizationParticipated(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）
     */
    public void handleGrassrootsDemocracyOutstanding(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleGrassrootsDemocracyOutstanding(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）
     */
    public void handleGrassrootsDemocracyGood(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleGrassrootsDemocracyGood(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）
     */
    public void handleGrassrootsDemocracyParticipated(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleGrassrootsDemocracyParticipated(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加书香政协学习、宣讲活动（特别突出）
     */
    public void handleReadingActivitiesOutstanding(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleReadingActivitiesOutstanding(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加书香政协学习、宣讲活动（表现较好）
     */
    public void handleReadingActivitiesGood(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleReadingActivitiesGood(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加书香政协学习、宣讲活动（有所参与）
     */
    public void handleReadingActivitiesParticipated(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleReadingActivitiesParticipated(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加新冠肺炎疫情防控（特别突出）
     */
    public void handleEpidemicPreventionOutstanding(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleEpidemicPreventionOutstanding(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加新冠肺炎疫情防控（表现较好）
     */
    public void handleEpidemicPreventionGood(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleEpidemicPreventionGood(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加新冠肺炎疫情防控（有所参与）
     */
    public void handleEpidemicPreventionParticipated(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleEpidemicPreventionParticipated(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 助力项目建设服务，参加引进外资活动（特别突出）
     */
    public void handleProjectServiceOutstanding(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleProjectServiceOutstanding(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 助力项目建设服务，参加引进外资活动（表现较好）
     */
    public void handleProjectServiceGood(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleProjectServiceGood(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 助力项目建设服务，参加引进外资活动（有所参与）
     */
    public void handleProjectServiceParticipated(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleProjectServiceParticipated(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 助推创一流营商环境（特别突出）
     */
    public void handleBusinessEnvironmentOutstanding(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleBusinessEnvironmentOutstanding(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 助推创一流营商环境（表现较好）
     */
    public void handleBusinessEnvironmentGood(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleBusinessEnvironmentGood(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 助推创一流营商环境（有所参与）
     */
    public void handleBusinessEnvironmentParticipated(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleBusinessEnvironmentParticipated(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 完成区政协交办的其他任务（特别突出）
     */
    public void handleOtherTasksOutstanding(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleOtherTasksOutstanding(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 完成区政协交办的其他任务（表现较好）
     */
    public void handleOtherTasksGood(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleOtherTasksGood(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 完成区政协交办的其他任务（有所参与）
     */
    public void handleOtherTasksParticipated(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = rewardStrategyMapper.handleOtherTasksParticipated(member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    // ==================== 批量查询优化方法 ====================

    /**
     * 预加载指定年份的所有奖励分数据
     * 
     * @param year 年份
     */
    public void preloadData(String year) {
        if (year.equals(currentYear) && !preloadedDataCache.isEmpty()) {
            log.debug("数据已预加载，跳过重复加载");
            return;
        }

        log.info("开始预加载奖励分数据，年份: {}", year);
        preloadedDataCache.clear();
        currentYear = year;

        try {
            // 预加载各种奖励分数据
            // TODO:查询 数据

            log.info("奖励分数据预加载完成，方法数: {}", preloadedDataCache.size());
        } catch (Exception e) {
            log.warn("预加载奖励分数据失败: {}", e.getMessage());
            preloadedDataCache.clear();
        }
    }

    /**
     * 从预加载数据中获取分数，如果没有则使用原始查询
     */
    private Integer getScoreFromCache(String methodName, Long memberId, CommitteeMember member) {
        Map<Long, Integer> methodData = preloadedDataCache.get(methodName);
        if (methodData != null) {
            return methodData.getOrDefault(memberId, 0);
        }

        // 降级到原始查询
        try {
            if ("handleEnvProtectionSupervisionOutstanding".equals(methodName)) {
                return rewardStrategyMapper.handleEnvProtectionSupervisionOutstanding(member);
            } else if ("handleEnvProtectionSupervisionGood".equals(methodName)) {
                return rewardStrategyMapper.handleEnvProtectionSupervisionGood(member);
            }
        } catch (Exception e) {
            log.error("查询失败: {}", e.getMessage());
        }
        return 0;
    }
}
