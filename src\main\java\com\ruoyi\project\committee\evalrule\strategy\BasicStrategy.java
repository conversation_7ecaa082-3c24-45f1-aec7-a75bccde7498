package com.ruoyi.project.committee.evalrule.strategy;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.mapper.BasicStrategyMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class BasicStrategy {

    private final BasicStrategyMapper basicStrategyMapper;

    // 预加载的数据缓存
    private Map<String, Map<Long, Integer>> preloadedDataCache = new HashMap<>();
    private String currentYear;

    /**
     * 缺席区政协全体会议1次（未请假）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAbsencePlenaryUnexcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAbsencePlenaryUnexcused", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协全体会议1次（请假）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAbsencePlenaryExcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAbsencePlenaryExcused", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协常委会议1次（未请假）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAbsenceStandingUnexcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAbsenceStandingUnexcused", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协常委会议1次（请假）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAbsenceStandingExcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAbsenceStandingExcused", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协专委会会议1次（未请假）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAbsenceCommitteeUnexcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAbsenceCommitteeUnexcused", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 缺席区政协专委会会议1次（请假）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAbsenceCommitteeExcused(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAbsenceCommitteeExcused", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 区政协委员列席区政协常委扩大会议
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAttendanceStandingExtended(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAttendanceStandingExtended", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加专委会组织的其他会议（非全体会议）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAttendanceCommitteeOther(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAttendanceCommitteeOther", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加全国政协、省政协、市政协组织的相关会议
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void handleAttendanceHigherMeeting(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAttendanceHigherMeeting", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 参加全国政协、省政协、市政协、区政协组织相关活动
     *
     * @param member 政协委员对象
     */
    public void handleAttendanceMultiActivity(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleAttendanceMultiActivity", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 受区政协委托参加区委、区政府组成部门等单位组织的活动
     *
     * @param member 政协委员对象
     *               remark >> 没有该类活动
     */
    public void handleAttendanceDelegatedExternal(CommitteeMember member, RuleDetailVo ruleDetail) {
        ruleDetail.setFinalScore(0);
    }

    /**
     * 参加区政协组织的委员培训活动
     *
     * @param member 政协委员对象
     *               remark >> 没有该类活动
     */
    public void handleTrainingDistrictRegular(CommitteeMember member, RuleDetailVo ruleDetail) {
        ruleDetail.setFinalScore(0);
    }

    /**
     * 参加与区政协工作相关的各类会议与活动情况
     *
     * @param member 政协委员对象
     */
    public void handleParticipationRelatedAll(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleParticipationRelatedAll", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 处理全会/常委会/联组会议的书面+口头发言
     *
     * @param member 政协委员对象
     */
    public void handleSpeechPlenaryBoth(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleSpeechPlenaryBoth", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 处理全会/常委会/联组会议的书面发言
     *
     * @param member 政协委员对象
     */
    public void handleSpeechPlenaryWritten(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleSpeechPlenaryWritten", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 处理其他会议的发言（按次数计）
     *
     * @param member 政协委员对象
     */
    public void handleSpeechOtherCounted(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("handleSpeechOtherCounted", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 一年内不提交提案
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void hasNotSubmittedProposalWithinOneYear(CommitteeMember member, RuleDetailVo ruleDetail) {
        Boolean hasProposal = getBooleanFromCache("hasNotSubmittedProposalWithinOneYear", member.getId(), member);
        if (hasProposal) {
            ruleDetail.setFinalScore(Integer.valueOf(ruleDetail.getScore()));
        } else {
            ruleDetail.setFinalScore(0);
        }
    }

    /**
     * 获立案的个人提案（第一提案人）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void countApprovedProposalsAsPrimaryProposer(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("countApprovedProposalsAsPrimaryProposer", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 获立案的个人提案（附议人）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void countApprovedProposalsAsSecondaryProposer(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("countApprovedProposalsAsSecondaryProposer", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 1年内不提交社情民意（按年度累计计分）
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void hasNotSubmittedManuscriptWithinOneYear(CommitteeMember member, RuleDetailVo ruleDetail) {
        Boolean hasManuscript = getBooleanFromCache("hasNotSubmittedManuscriptWithinOneYear", member.getId(), member);
        if (hasManuscript) {
            ruleDetail.setFinalScore(Integer.valueOf(ruleDetail.getScore()));
        } else {
            ruleDetail.setFinalScore(0);
        }
    }

    /**
     * 社情民意被采纳
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void countAcceptedManuscripts(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("countAcceptedManuscripts", member.getId(), member);
        int finalScore = count * NumberUtils.toInt(ruleDetail.getScore());
        ruleDetail.setFinalScore(Math.min(finalScore, 6));
    }

    /**
     * 获得区领导批示
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void countDistrictEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("countDistrictEndorsedManuscript", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 获得市领导批示
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void countCityEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("countCityEndorsedManuscript", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    /**
     * 获得省领导批示
     *
     * @param member     member
     * @param ruleDetail ruleDetail
     */
    public void countProvinceEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = getScoreFromCache("countProvinceEndorsedManuscript", member.getId(), member);
        ruleDetail.setFinalScore(count * NumberUtils.toInt(ruleDetail.getScore()));
    }

    // ==================== 批量查询优化方法 ====================

    /**
     * 预加载指定年份的所有基础分数据
     * 
     * @param year 年份
     */
    public void preloadData(String year) {
        if (year.equals(currentYear) && !preloadedDataCache.isEmpty()) {
            log.debug("数据已预加载，跳过重复加载");
            return;
        }

        log.info("开始预加载基础分数据，年份: {}", year);
        preloadedDataCache.clear();
        currentYear = year;

        try {
            // 预加载各种基础分数据
            preloadedDataCache.put("handleAbsencePlenaryUnexcused",
                    basicStrategyMapper.handleAbsencePlenaryUnexcused(year));
            preloadedDataCache.put("handleAbsencePlenaryExcused",
                    basicStrategyMapper.handleAbsencePlenaryExcused(year));
            preloadedDataCache.put("handleAbsenceStandingUnexcused",
                    basicStrategyMapper.handleAbsenceStandingUnexcused(year));
            preloadedDataCache.put("handleAbsenceStandingExcused",
                    basicStrategyMapper.handleAbsenceStandingExcused(year));
            preloadedDataCache.put("handleAbsenceCommitteeUnexcused",
                    basicStrategyMapper.handleAbsenceCommitteeUnexcused(year));
            preloadedDataCache.put("handleAbsenceCommitteeExcused",
                    basicStrategyMapper.handleAbsenceCommitteeExcused(year));
            preloadedDataCache.put("handleAttendanceStandingExtended",
                    basicStrategyMapper.handleAttendanceStandingExtended(year));
            preloadedDataCache.put("handleAttendanceCommitteeOther",
                    basicStrategyMapper.handleAttendanceCommitteeOther(year));
            preloadedDataCache.put("handleAttendanceHigherMeeting",
                    basicStrategyMapper.handleAttendanceHigherMeeting(year));
            preloadedDataCache.put("handleAttendanceMultiActivity",
                    basicStrategyMapper.handleAttendanceMultiActivity(year));
            preloadedDataCache.put("handleParticipationRelatedAll",
                    basicStrategyMapper.handleParticipationRelatedAll(year));
            preloadedDataCache.put("handleSpeechPlenaryBoth",
                    basicStrategyMapper.handleSpeechPlenaryBoth(year));
            preloadedDataCache.put("handleSpeechPlenaryWritten",
                    basicStrategyMapper.handleSpeechPlenaryWritten(year));
            preloadedDataCache.put("handleSpeechOtherCounted",
                    basicStrategyMapper.handleSpeechOtherCounted(year));
            preloadedDataCache.put("countApprovedProposalsAsPrimaryProposer",
                    basicStrategyMapper.countApprovedProposalsAsPrimaryProposer(year));
            preloadedDataCache.put("countApprovedProposalsAsSecondaryProposer",
                    basicStrategyMapper.countApprovedProposalsAsSecondaryProposer(year));
            preloadedDataCache.put("countAcceptedManuscripts",
                    basicStrategyMapper.countAcceptedManuscripts(year));
            preloadedDataCache.put("countDistrictEndorsedManuscript",
                    basicStrategyMapper.countDistrictEndorsedManuscript(year));
            preloadedDataCache.put("countCityEndorsedManuscript",
                    basicStrategyMapper.countCityEndorsedManuscript(year));
            preloadedDataCache.put("countProvinceEndorsedManuscript",
                    basicStrategyMapper.countProvinceEndorsedManuscript(year));

            // 预加载布尔类型数据 (需要特殊处理，因为返回类型不同)
            // 这些方法返回 Map<Long, Boolean>，但我们的缓存是 Map<String, Map<Long, Integer>>
            // 所以暂时不在这里预加载，直接在使用时查询

            log.info("基础分数据预加载完成，方法数: {}", preloadedDataCache.size());
        } catch (Exception e) {
            log.warn("预加载基础分数据失败: {}", e.getMessage());
            preloadedDataCache.clear();
        }
    }

    /**
     * 从预加载数据中获取分数，如果没有则使用原始查询
     */
    private Integer getScoreFromCache(String methodName, Long memberId, CommitteeMember member) {
        Map<Long, Integer> methodData = preloadedDataCache.get(methodName);
        if (methodData != null) {
            return methodData.getOrDefault(memberId, 0);
        }

        // 降级到原始查询 - 直接调用批量方法并取对应值
        try {
            Map<Long, Integer> result = null;
            if ("handleAbsencePlenaryUnexcused".equals(methodName)) {
                result = basicStrategyMapper.handleAbsencePlenaryUnexcused(member.getYear());
            } else if ("handleAbsencePlenaryExcused".equals(methodName)) {
                result = basicStrategyMapper.handleAbsencePlenaryExcused(member.getYear());
            } else if ("handleAbsenceStandingUnexcused".equals(methodName)) {
                result = basicStrategyMapper.handleAbsenceStandingUnexcused(member.getYear());
            } else if ("handleAbsenceStandingExcused".equals(methodName)) {
                result = basicStrategyMapper.handleAbsenceStandingExcused(member.getYear());
            } else if ("handleAbsenceCommitteeUnexcused".equals(methodName)) {
                result = basicStrategyMapper.handleAbsenceCommitteeUnexcused(member.getYear());
            } else if ("handleAbsenceCommitteeExcused".equals(methodName)) {
                result = basicStrategyMapper.handleAbsenceCommitteeExcused(member.getYear());
            } else if ("handleAttendanceStandingExtended".equals(methodName)) {
                result = basicStrategyMapper.handleAttendanceStandingExtended(member.getYear());
            } else if ("handleAttendanceCommitteeOther".equals(methodName)) {
                result = basicStrategyMapper.handleAttendanceCommitteeOther(member.getYear());
            } else if ("handleAttendanceHigherMeeting".equals(methodName)) {
                result = basicStrategyMapper.handleAttendanceHigherMeeting(member.getYear());
            } else if ("handleAttendanceMultiActivity".equals(methodName)) {
                result = basicStrategyMapper.handleAttendanceMultiActivity(member.getYear());
            } else if ("handleParticipationRelatedAll".equals(methodName)) {
                result = basicStrategyMapper.handleParticipationRelatedAll(member.getYear());
            } else if ("handleSpeechPlenaryBoth".equals(methodName)) {
                result = basicStrategyMapper.handleSpeechPlenaryBoth(member.getYear());
            } else if ("handleSpeechPlenaryWritten".equals(methodName)) {
                result = basicStrategyMapper.handleSpeechPlenaryWritten(member.getYear());
            } else if ("handleSpeechOtherCounted".equals(methodName)) {
                result = basicStrategyMapper.handleSpeechOtherCounted(member.getYear());
            } else if ("countApprovedProposalsAsPrimaryProposer".equals(methodName)) {
                result = basicStrategyMapper.countApprovedProposalsAsPrimaryProposer(member.getYear());
            } else if ("countApprovedProposalsAsSecondaryProposer".equals(methodName)) {
                result = basicStrategyMapper.countApprovedProposalsAsSecondaryProposer(member.getYear());
            } else if ("countAcceptedManuscripts".equals(methodName)) {
                result = basicStrategyMapper.countAcceptedManuscripts(member.getYear());
            } else if ("countDistrictEndorsedManuscript".equals(methodName)) {
                result = basicStrategyMapper.countDistrictEndorsedManuscript(member.getYear());
            } else if ("countCityEndorsedManuscript".equals(methodName)) {
                result = basicStrategyMapper.countCityEndorsedManuscript(member.getYear());
            } else if ("countProvinceEndorsedManuscript".equals(methodName)) {
                result = basicStrategyMapper.countProvinceEndorsedManuscript(member.getYear());
            }

            if (result != null) {
                return result.getOrDefault(memberId, 0);
            }
        } catch (Exception e) {
            log.error("查询失败: {}", e.getMessage());
        }
        return 0;
    }

    /**
     * 从预加载数据中获取布尔值，如果没有则使用原始查询
     */
    private Boolean getBooleanFromCache(String methodName, Long memberId, CommitteeMember member) {
        // 对于布尔类型，需要特殊处理
        try {
            Map<Long, Boolean> result = null;
            if ("hasNotSubmittedProposalWithinOneYear".equals(methodName)) {
                result = basicStrategyMapper.hasNotSubmittedProposalWithinOneYear(member.getYear());
            } else if ("hasNotSubmittedManuscriptWithinOneYear".equals(methodName)) {
                result = basicStrategyMapper.hasNotSubmittedManuscriptWithinOneYear(member.getYear());
            }

            if (result != null) {
                return result.getOrDefault(memberId, false);
            }
        } catch (Exception e) {
            log.error("查询失败: {}", e.getMessage());
        }
        return false;
    }
}
