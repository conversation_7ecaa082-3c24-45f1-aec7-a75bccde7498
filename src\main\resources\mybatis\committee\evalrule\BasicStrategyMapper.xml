<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.evalrule.mapper.BasicStrategyMapper">

    <select id="handleAbsencePlenaryUnexcused" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COUNT(DISTINCT score_data.meeting_id) AS total_count
        FROM
            sys_committee_member cm
        LEFT JOIN (
            SELECT
                mp.people_id,
                mi.id AS meeting_id  -- 获取会议ID用于去重
            FROM
                meeting_info mi
            JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = FALSE AND mp.del_flag = FALSE
            JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
            JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = FALSE
            WHERE mi.del_flag = FALSE
            AND YEAR(mi.start_time) = #{year}
            AND mi.meeting_type = 'FULL_COMM'
        ) score_data ON cm.id = score_data.people_id
        WHERE cm.year = #{year}
        GROUP BY cm.id;  -- 按成员分组
    </select>

    <select id="handleAbsencePlenaryExcused" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(absence_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = TRUE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type = 'FULL_COMM'
                GROUP BY mp.people_id
            ) absence_data ON cm.id = absence_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleAbsenceStandingUnexcused" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(absence_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = FALSE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type = 'STANDING_COMM'
                GROUP BY mp.people_id
            ) absence_data ON cm.id = absence_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleAbsenceStandingExcused" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(absence_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = TRUE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type = 'STANDING_COMM'
                GROUP BY mp.people_id
            ) absence_data ON cm.id = absence_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleAbsenceCommitteeUnexcused" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(absence_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = FALSE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type = 'SPEC_COMM'
                GROUP BY mp.people_id
            ) absence_data ON cm.id = absence_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleAbsenceCommitteeExcused" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(absence_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = TRUE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type = 'SPEC_COMM'
                GROUP BY mp.people_id
            ) absence_data ON cm.id = absence_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleAttendanceStandingExtended" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(attendance_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = TRUE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = TRUE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type = 'EXP_STAND_COMM'
                GROUP BY mp.people_id
            ) attendance_data ON cm.id = attendance_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleAttendanceCommitteeOther" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(attendance_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = TRUE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = TRUE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type = 'OTHER'
                GROUP BY mp.people_id
            ) attendance_data ON cm.id = attendance_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleAttendanceHigherMeeting" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(attendance_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = TRUE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = TRUE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type IN ('TOPIC_CONSULT', 'TARGET_CONSULT', 'PROP_HANDLE_FORUM')
                GROUP BY mp.people_id
            ) attendance_data ON cm.id = attendance_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleAttendanceMultiActivity" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(activity_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    CAST(ap.Pepole_PKID AS UNSIGNED) AS people_id,
                    COUNT(*) AS total_count
                FROM activity_basicinfo a
                JOIN activity_participants ap ON a.id = ap.Activity_PKID AND ap.Is_Attend = TRUE
                WHERE YEAR(a.create_time) = #{year}
                    AND a.`Type` IN ('1', '2', '3', '4')
                GROUP BY ap.Pepole_PKID
            ) activity_data ON cm.id = activity_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

<!--    <select id="handleAttendanceDelegatedExternal" resultType="java.lang.Integer"></select>-->
<!--    <select id="handleTrainingDistrictRegular" resultType="java.lang.Integer"></select>-->

    <select id="handleParticipationRelatedAll" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(activity_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    CAST(ap.Pepole_PKID AS UNSIGNED) AS people_id,
                    COUNT(*) AS total_count
                FROM activity_basicinfo a
                JOIN activity_participants ap ON a.id = ap.Activity_PKID AND ap.Is_Attend = TRUE
                WHERE YEAR(a.create_time) = #{year}
                    AND a.`Type` = '10'
                GROUP BY ap.Pepole_PKID
            ) activity_data ON cm.id = activity_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleSpeechPlenaryBoth" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(speech_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    d.speaker AS people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_doc d ON mi.id = d.meeting_id AND d.doc_type = 'WRITTEN_SPEECH' AND d.del_flag = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type IN ('FULL_COMM', 'STANDING_COMM')
                GROUP BY d.speaker
            ) speech_data ON cm.id = speech_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleSpeechPlenaryWritten" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(speech_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    d.speaker AS people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_doc d ON mi.id = d.meeting_id AND d.doc_type = 'ORAL_SPEECH' AND d.del_flag = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type IN ('FULL_COMM', 'STANDING_COMM')
                GROUP BY d.speaker
            ) speech_data ON cm.id = speech_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="handleSpeechOtherCounted" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(speech_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    d.speaker AS people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_doc d ON mi.id = d.meeting_id AND d.doc_type IN ('ORAL_SPEECH', 'WRITTEN_SPEECH') AND d.del_flag = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type IN ('TOPIC_CONSULT', 'TARGET_CONSULT', 'PROP_HANDLE_FORUM', 'OTHER')
                GROUP BY d.speaker
            ) speech_data ON cm.id = speech_data.people_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="hasNotSubmittedProposalWithinOneYear" resultType="boolean">
        SELECT
            cm.id AS member_id,
            COALESCE(proposal_data.has_proposal, FALSE) AS has_proposal
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    u.id AS user_id,
                    TRUE AS has_proposal
                FROM sys_user u
                JOIN proposal_user_rel pur ON u.id = pur.proposer_id AND pur.del_flag = FALSE
                JOIN proposal p ON pur.proposal_id = p.id AND p.del_flag = FALSE
                WHERE p.year = #{year}
                GROUP BY u.id
            ) proposal_data ON cm.user_id = proposal_data.user_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="countApprovedProposalsAsPrimaryProposer" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(proposal_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    u.id AS user_id,
                    COUNT(*) AS total_count
                FROM sys_user u
                JOIN proposal_user_rel pur ON u.id = pur.proposer_id AND pur.del_flag = FALSE AND pur.submit_type = '领衔'
                JOIN proposal p ON pur.proposal_id = p.id AND p.del_flag = FALSE
                WHERE p.submit_type = 'INDIVIDUAL'
                    AND p.year = #{year}
                    AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
                GROUP BY u.id
            ) proposal_data ON cm.user_id = proposal_data.user_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="countApprovedProposalsAsSecondaryProposer" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(proposal_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    u.id AS user_id,
                    COUNT(*) AS total_count
                FROM sys_user u
                JOIN proposal_user_rel pur ON u.id = pur.proposer_id AND pur.del_flag = FALSE AND pur.submit_type = '附议'
                JOIN proposal p ON pur.proposal_id = p.id AND p.del_flag = FALSE
                WHERE p.submit_type = 'INDIVIDUAL'
                    AND p.year = #{year}
                    AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
                GROUP BY u.id
            ) proposal_data ON cm.user_id = proposal_data.user_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="hasNotSubmittedManuscriptWithinOneYear" resultType="java.lang.Boolean">
        SELECT
            cm.id AS member_id,
            COALESCE(manuscript_data.has_manuscript, FALSE) AS has_manuscript
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    u.id AS user_id,
                    TRUE AS has_manuscript
                FROM sys_user u
                JOIN manuscript_reflector_rel mrl ON u.id = mrl.user_id AND mrl.del_flag = FALSE
                JOIN manuscript m ON mrl.manuscript_id = m.id AND m.del_flag = FALSE
                WHERE m.year = #{year}
                GROUP BY u.id
            ) manuscript_data ON cm.user_id = manuscript_data.user_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="countAcceptedManuscripts" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(manuscript_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    u.id AS user_id,
                    COUNT(*) AS total_count
                FROM sys_user u
                JOIN manuscript_reflector_rel mrl ON u.id = mrl.user_id AND mrl.del_flag = FALSE
                JOIN manuscript m ON mrl.manuscript_id = m.id AND m.del_flag = FALSE
                WHERE m.status != 'DISCARD'
                    AND m.adopt_way != 'NOT_ADOPT'
                    AND m.year = #{year}
                GROUP BY u.id
            ) manuscript_data ON cm.user_id = manuscript_data.user_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="countDistrictEndorsedManuscript" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(manuscript_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    u.id AS user_id,
                    COUNT(*) AS total_count
                FROM sys_user u
                JOIN manuscript_reflector_rel mrl ON u.id = mrl.user_id AND mrl.del_flag = FALSE
                JOIN manuscript m ON mrl.manuscript_id = m.id AND m.del_flag = FALSE
                JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = FALSE
                WHERE m.year = #{year}
                    AND me.endorse_type = 'DISTRICT'
                GROUP BY u.id
            ) manuscript_data ON cm.user_id = manuscript_data.user_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="countCityEndorsedManuscript" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(manuscript_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    u.id AS user_id,
                    COUNT(*) AS total_count
                FROM sys_user u
                JOIN manuscript_reflector_rel mrl ON u.id = mrl.user_id AND mrl.del_flag = FALSE
                JOIN manuscript m ON mrl.manuscript_id = m.id AND m.del_flag = FALSE
                JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = FALSE
                WHERE m.year = #{year}
                    AND FIND_IN_SET('CITY', me.endorse_type) > 0
                    AND FIND_IN_SET('PROVINCE', me.endorse_type) &lt; 0
                GROUP BY u.id
            ) manuscript_data ON cm.user_id = manuscript_data.user_id
        WHERE cm.YEAR = #{year}
    </select>

    <select id="countProvinceEndorsedManuscript" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE(manuscript_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    u.id AS user_id,
                    COUNT(*) AS total_count
                FROM sys_user u
                JOIN manuscript_reflector_rel mrl ON u.id = mrl.user_id AND mrl.del_flag = FALSE
                JOIN manuscript m ON mrl.manuscript_id = m.id AND m.del_flag = FALSE
                JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = FALSE
                WHERE m.year = #{year}
                    AND FIND_IN_SET('PROVINCE', me.endorse_type) > 0
                GROUP BY u.id
            ) manuscript_data ON cm.user_id = manuscript_data.user_id
        WHERE cm.YEAR = #{year}
    </select>

</mapper>