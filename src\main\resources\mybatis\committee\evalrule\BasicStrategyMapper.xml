<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.evalrule.mapper.BasicStrategyMapper">

    <select id="handleAbsencePlenaryUnexcused" resultType="java.lang.Integer">
        SELECT
            cm.id AS member_id,
            COALESCE( absence_data.total_count, 0) AS total_count
        FROM sys_committee_member cm
            LEFT JOIN (
                SELECT
                    mp.people_id,
                    COUNT(*) AS total_count
                FROM meeting_info mi
                JOIN meeting_participants mp ON mi.id = mp.meeting_id AND mp.is_attend = FALSE AND mp.del_flag = FALSE
                JOIN meeting_sign ms ON mi.id = ms.meeting_id AND ms.del_flag = FALSE
                JOIN meeting_sign_detail msd ON ms.id = msd.sign_id AND msd.people_id = mp.people_id AND msd.del_flag = FALSE AND msd.is_sign = FALSE
                WHERE mi.del_flag = FALSE
                    AND YEAR(mi.start_time) = #{year}
                    AND mi.meeting_type = 'FULL_COMM'
                GROUP BY mp.people_id
        ) score_data ON cm.id = score_data.people_id
        AND cm.YEAR = #{year}
    </select>

    <select id="handleAbsencePlenaryExcused" resultType="java.lang.Integer" >
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'FULL_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAbsenceStandingUnexcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = false AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'STANDING_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAbsenceStandingExcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'STANDING_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAbsenceCommitteeUnexcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = false AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'SPEC_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAbsenceCommitteeExcused" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'SPEC_COMM'
            AND sd.is_sign = false
    </select>

    <select id="handleAttendanceStandingExtended" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'EXP_STAND_COMM'
            AND sd.is_sign = true
    </select>

    <select id="handleAttendanceCommitteeOther" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type = 'OTHER'
            AND sd.is_sign = true
    </select>

    <select id="handleAttendanceHigherMeeting" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_participants mp ON m.id = mp.meeting_id AND mp.people_id = #{member.id} AND mp.is_attend = true AND mp.del_flag = false
        INNER JOIN meeting_sign s ON m.id = s.meeting_id AND s.del_flag = false
        INNER JOIN meeting_sign_detail sd ON s.id = sd.sign_id AND sd.people_id = #{member.id} AND sd.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type IN ('TOPIC_CONSULT', 'TARGET_CONSULT', 'PROP_HANDLE_FORUM')
            AND sd.is_sign = true
    </select>

    <select id="handleAttendanceMultiActivity" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM activity_basicinfo a
        INNER JOIN activity_participants ap ON a.id = ap.Activity_PKID AND ap.Pepole_PKID = CONCAT(#{member.id}, '') AND ap.Is_Attend = true
        WHERE YEAR(a.create_time) = #{member.year}
            AND a.`Type` IN ('1', '2', '3', '4')
    </select>

<!--    <select id="handleAttendanceDelegatedExternal" resultType="java.lang.Integer"></select>-->
<!--    <select id="handleTrainingDistrictRegular" resultType="java.lang.Integer"></select>-->

    <select id="handleParticipationRelatedAll" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM activity_basicinfo a
        INNER JOIN activity_participants ap ON a.id = ap.Activity_PKID AND ap.Pepole_PKID = CONCAT(#{member.id}, '') AND ap.Is_Attend = true
        WHERE YEAR(a.create_time) = #{member.year}
            AND a.`Type` = '10'
    </select>

    <select id="handleSpeechPlenaryBoth" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_doc d ON m.id = d.meeting_id AND d.doc_type = 'WRITTEN_SPEECH' AND d.speaker = #{member.id}  AND d.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type IN ('FULL_COMM', 'STANDING_COMM')
    </select>

    <select id="handleSpeechPlenaryWritten" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_doc d ON m.id = d.meeting_id AND d.doc_type = 'ORAL_SPEECH' AND d.speaker = #{member.id}  AND d.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type IN ('FULL_COMM', 'STANDING_COMM')
    </select>

    <select id="handleSpeechOtherCounted" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_info m
        INNER JOIN meeting_doc d ON m.id = d.meeting_id AND d.doc_type IN ('ORAL_SPEECH', 'WRITTEN_SPEECH') AND d.speaker = #{member.id}  AND d.del_flag = false
        WHERE m.del_flag = false
            AND YEAR(m.start_time) = #{member.year}
            AND m.meeting_type IN ('TOPIC_CONSULT', 'TARGET_CONSULT', 'PROP_HANDLE_FORUM', 'OTHER')
    </select>

    <select id="hasNotSubmittedProposalWithinOneYear" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM proposal p
                INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id
            WHERE p.del_flag = false
                AND pur.del_flag = false
                AND pur.proposer_id = #{member.userId}
                AND p.year = #{member.year}
        )
    </select>

    <select id="countApprovedProposalsAsPrimaryProposer" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM proposal p
            INNER JOIN proposal_user_rel pur ON
                p.id = pur.proposal_id AND
                pur.del_flag = false AND
                pur.proposer_id = #{member.userId} AND
                pur.submit_type = '领衔'
        WHERE p.del_flag = false
            AND p.submit_type = 'INDIVIDUAL'
            AND p.year = #{member.year}
            AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
    </select>

    <select id="countApprovedProposalsAsSecondaryProposer" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM proposal p
            INNER JOIN proposal_user_rel pur ON
                p.id = pur.proposal_id AND
                pur.del_flag = false AND
                pur.proposer_id = '附议'
        WHERE p.del_flag = false
            AND p.submit_type = 'INDIVIDUAL'
            AND p.year = #{member.year}
            AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
    </select>

    <select id="hasNotSubmittedManuscriptWithinOneYear" resultType="java.lang.Boolean">
        SELECT EXISTS (
            SELECT 1
            FROM proposal p
                INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id
            WHERE p.del_flag = false
                AND pur.del_flag = false
                AND pur.proposer_id = #{member.userId}
                AND p.year = #{member.year}
        )
    </select>

    <select id="countAcceptedManuscripts" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM manuscript m
            INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false
        WHERE m.del_flag = false
            AND m.status != 'DISCARD'
            AND m.adopt_way != 'NOT_ADOPT'
            AND m.year = #{member.year}
            AND mrl.user_id = #{member.userId}
    </select>

    <select id="countDistrictEndorsedManuscript" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM manuscript m
        INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false
        INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false
        WHERE m.del_flag = false
            AND m.year = #{member.year}
            AND mrl.user_id = #{member.userId}
            AND me.endorse_type = 'DISTRICT'
    </select>


    <select id="countCityEndorsedManuscript" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM manuscript m
            INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false
            INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false
        WHERE m.del_flag = false
            AND m.year = #{member.year}
            AND mrl.user_id = #{member.userId}
            AND FIND_IN_SET('CITY', me.endorse_type) > 0
            AND FIND_IN_SET('PROVINCE', me.endorse_type) &lt; 0
    </select>

    <select id="countProvinceEndorsedManuscript" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM manuscript m
            INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false
            INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false
        WHERE m.del_flag = false
            AND m.year = #{member.year}
            AND mrl.user_id = #{member.userId}
            AND FIND_IN_SET('PROVINCE', me.endorse_type) > 0
    </select>

</mapper>