package com.ruoyi.project.activity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 已参加活动信息VO
 */
@Data
public class ActivityJoinedVO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 原UUID主键
     */
    private String pkid;

    /**
     * 活动主题
     */
    private String title;

    /**
     * 活动类型
     */
    private String type;
    
    /**
     * 发起部门ID
     */
    private Long deptId;

    /**
     * 发起部门名称
     */
    private String deptName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityBeginDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndDate;
    
    /**
     * 参加状态(1:参加 0:不参加 2:请假)
     */
    private String isAttend;
    
    /**
     * 参加状态文本
     */
    private String attendStatusText;
    
    /**
     * 不参加/请假原因
     */
    private String noAttendReason;

    /**
     * 活动地址
     */
    private String address;
}
