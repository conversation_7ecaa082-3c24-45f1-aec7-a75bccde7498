package com.ruoyi.project.activity.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.activity.domain.ActivityParticipants;
import com.ruoyi.project.activity.domain.dto.ActivityJoinedPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityParticipantAbsenceDTO;
import com.ruoyi.project.activity.domain.dto.ActivityRegistrationDTO;
import com.ruoyi.project.activity.domain.dto.ActivityRegistrationPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityBasicinfoVO;
import com.ruoyi.project.activity.domain.vo.ActivityJoinedVO;
import com.ruoyi.project.activity.domain.vo.ActivityParticipantsVO;

/**
 * 活动参与人员服务接口
 */
public interface IActivityParticipantsService extends IService<ActivityParticipants> {

    /**
     * 更新参与人员的参加状态和不参加/请假原因
     *
     * @param absenceDTO 不参加/请假DTO
     * @return 是否更新成功
     */
    boolean updateParticipantAttendStatus(ActivityParticipantAbsenceDTO absenceDTO);

    /**
     * 获取当前登录用户可以参加报名的活动分页列表
     *
     * @param userId 用户ID
     * @param pageDTO 分页查询参数
     * @return 活动分页列表
     */
    IPage<ActivityBasicinfoVO> getCurrentUserRegistrationList(String userId, ActivityRegistrationPageDTO pageDTO);

    /**
     * 获取当前登录用户已参加的活动分页列表
     *
     * @param userId 用户ID
     * @param pageDTO 分页查询参数
     * @return 活动分页列表
     */
    IPage<ActivityJoinedVO> getCurrentUserJoinedActivityList(String userId, ActivityJoinedPageDTO pageDTO);

    /**
     * 用户报名参加活动
     *
     * @param userId 用户ID
     * @param registrationDTO 报名信息
     * @return 是否报名成功
     */
    boolean registerActivity(String userId, ActivityRegistrationDTO registrationDTO);

    /**
     * 根据活动ID查询当前登录用户参加活动的详细情况
     *
     * @param activityId 活动ID
     * @param userId 当前登录用户ID
     * @return 参与详情
     */
    ActivityParticipantsVO getParticipantDetailByActivityAndCurrentUser(String activityId, String userId);
}