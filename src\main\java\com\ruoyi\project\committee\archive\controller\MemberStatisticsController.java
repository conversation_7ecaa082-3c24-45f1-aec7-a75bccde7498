package com.ruoyi.project.committee.archive.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.committee.archive.service.IMemberStatisticsService;
import com.ruoyi.project.committee.archive.domain.dto.MemberStatisticsEditVo;
import com.ruoyi.project.committee.archive.domain.dto.MemberStatisticsPageDto;
import com.ruoyi.project.committee.archive.domain.vo.MemberStatisticsVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 履职档案信息
 */
@RestController
@RequestMapping(value = "/memberStat")
public class MemberStatisticsController extends BaseController {

    @Resource
    private IMemberStatisticsService memberStatisticsService;

    /**
     * 分页查询
     * @param pageDto pageDto
     * @return result
     */
    @PostMapping(value = "/page")
    public TableDataInfo selectMemberStatisticsPage(@RequestBody MemberStatisticsPageDto pageDto) {
        IPage<MemberStatisticsVo> pageResult = memberStatisticsService.selectMemberStatisticsPage(pageDto);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 新增记录
     * @param editVo editVo
     * @return result
     */
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody MemberStatisticsEditVo editVo) {
        return success(memberStatisticsService.add(editVo));
    }

    /**
     * 修改记录
     * @param editVo editVo
     * @return result
     */
    @PostMapping(value = "/edit")
    public AjaxResult edit(@RequestBody MemberStatisticsEditVo editVo) {
        return success(memberStatisticsService.edit(editVo));
    }

    /**
     * 获取记录
     * @param id id
     * @return result
     */
    @GetMapping("/get")
    public AjaxResult get(@RequestParam String id) {
        return success(memberStatisticsService.getById(id));
    }


}
