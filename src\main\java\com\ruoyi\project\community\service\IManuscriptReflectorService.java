package com.ruoyi.project.community.service;

import com.ruoyi.project.community.domain.vo.ManuscriptReflectorVo;

import java.util.List;
import java.util.Set;

public interface IManuscriptReflectorService {

    void saveBatchManuscriptReflector(String manuscriptId, List<String> reflectorIdList);

    void updateManuscriptReflector(String manuscriptId, List<String> reflectorIdList);

    List<ManuscriptReflectorVo> getManuscriptReflectorList(String manuscriptId);

    Set<String> getUserManuscriptIds(String userId);
}
