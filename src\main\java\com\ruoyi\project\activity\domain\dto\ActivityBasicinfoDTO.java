package com.ruoyi.project.activity.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 活动基本信息DTO
 */
@Data
public class ActivityBasicinfoDTO {
    
    /**
     * 自增主键（修改时使用）
     */
    private Long id;
    
    /**
     * 原UUID主键
     */
    private String pkid;
    
    /**
     * 活动主题
     */
    @NotBlank(message = "活动主题不能为空")
    private String title;
    
    /**
     * 日程安排
     */
    private String subject;
    
    /**
     * 活动类型
     */
    @NotBlank(message = "活动类型不能为空")
    private String type;
    
    /**
     * 开始时间
     */
    @NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityBeginDate;
    
    /**
     * 结束时间
     */
    @NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndDate;
    
    /**
     * 报名开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enterBeginDate;
    
    /**
     * 报名结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enterEndDate;
    
    /**
     * 活动地址
     */
    private String address;
    
    /**
     * 是否获得
     */
    private String isGain;
    
    /**
     * 发布状态
     */
    private String status;
    
    /**
     * 经度
     */
    private String geoX;
    
    /**
     * 纬度
     */
    private String geoY;
    
    /**
     * 半径
     */
    private String radius;
    
    /**
     * 活动城市
     */
    private String activityCity;
    
    /**
     * 联系人姓名
     */
    private String linkManName;
    
    /**
     * 联系人电话
     */
    private String linkManPhone;
    
    /**
     * 是否存在
     */
    private String isExist;
    
    /**
     * 类型详情
     */
    private String typeDetail;
    
    /**
     * 区域ID
     */
    private String regionId;
    
    /**
     * 补录状态
     */
    private String supplementStatus;
    
    /**
     * 参加委员ID列表
     */
    private List<String> participantIds;
}
