package com.ruoyi.project.proposal.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.IssueFeedback;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IssueFeedbackMapper extends BaseMapper<IssueFeedback> {

    default List<IssueFeedback> getFeedbackList(String createUser) {
        return selectList(new LambdaQueryWrapper<IssueFeedback>()
                .eq(IssueFeedback::getCreateBy, createUser));
    }
}
