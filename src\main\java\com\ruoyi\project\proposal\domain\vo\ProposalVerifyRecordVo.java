package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 提案审核记录信息对象
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
public class ProposalVerifyRecordVo {

    private String proposalId;

    private String verifier;

    private String verifyProcess;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date verifyTime;

    private String verifyLog;

}
