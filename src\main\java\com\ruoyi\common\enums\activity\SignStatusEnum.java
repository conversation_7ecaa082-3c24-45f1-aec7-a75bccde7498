package com.ruoyi.common.enums.activity;

import lombok.Getter;

/**
 * 签到状态枚举
 */
@Getter
public enum SignStatusEnum {
    /**
     * 未签到
     */
    UNSIGNED("UNSIGNED", "未签到"),
    /**
     * 已签到
     */
    SIGNED("SIGNED", "已签到"),
    /**
     * 请假
     */
    LEAVE("LEAVE", "请假");

    private final String code;
    private final String description;

    SignStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
