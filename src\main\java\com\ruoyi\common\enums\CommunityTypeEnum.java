package com.ruoyi.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 社区类型枚举
 */
@Getter
@AllArgsConstructor
public enum CommunityTypeEnum implements IEnum<String> {
    
    DO_GOOD_THINGS("1", "办好事"),
    SOLVE_PROBLEMS("2", "解难题"),
    DO_PUBLIC_WELFARE("3", "做公益");
    
    @EnumValue
    @JsonValue
    private final String code;
    private final String label;
    
    // 静态映射，用于快速查找
    private static final Map<String, CommunityTypeEnum> CODE_MAP = new HashMap<>();
    
    static {
        for (CommunityTypeEnum type : CommunityTypeEnum.values()) {
            CODE_MAP.put(type.code, type);
        }
    }
    
    /**
     * 根据code获取枚举实例
     */
    public static CommunityTypeEnum fromValue(String code) {
        return CODE_MAP.get(code);
    }
    
    /**
     * 允许整数类型的查询
     */
    public static CommunityTypeEnum fromValue(int code) {
        return fromValue(String.valueOf(code));
    }
    
    /**
     * 根据标签获取枚举实例
     */
    public static CommunityTypeEnum fromLabel(Object label) {
        if (label instanceof String) {
            for (CommunityTypeEnum type : CommunityTypeEnum.values()) {
                if (type.getLabel().equals(label)) {
                    return type;
                }
            }
        }
        return null;
    }
    
    @Override
    public String getValue() {
        return this.code;
    }
}
