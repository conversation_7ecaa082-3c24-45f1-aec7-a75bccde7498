package com.ruoyi.project.committee.archive.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 委员信息表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_committee_member")
public class CommitteeMember extends BaseEntity {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 年份
     */
    private String year;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 中文姓名
     */
    private String userName;

    /**
     * 委员编号
     */
    private String numberId;

    /**
     * 性别
     */
    private String sex;

    /**
     * 民族
     */
    private String nation;

    /**
     * 出生日期
     */
    private String birth;

    /**
     * 学历
     */
    private String education;

    /**
     * 单位职务
     */
    private String unitPost;

    /**
     * 单位电话
     */
    private String unitPhone;

    /**
     * 单位邮政编码
     */
    private String unitZipCode;

    /**
     * 单位地址
     */
    private String unitAddress;

    /**
     * 家庭电话
     */
    private String homeTel;

    /**
     * 家庭邮政编码
     */
    private String homeZipCode;

    /**
     * 家庭住址
     */
    private String homeAddress;

    /**
     * 个人手机
     */
    private String myPhone;

    /**
     * 分类
     */
    @TableField(value = "CateGory")
    private String cateGory;

    /**
     * 头像路径
     */
    private String imageSrc;

    /**
     * 当选届次
     */
    private String electedPeriod;

    /**
     * 是否启用
     */
    private String isEnable;

    /**
     * 状态
     */
    private String status;

    /**
     * 籍贯
     */
    private String originPlace;

    /**
     * 党派
     */
    private String party;

    /**
     * 界别
     */
    private String sector;

    /**
     * 界别呼叫类型
     */
    private String sectorCallType;

    /**
     * 学位
     */
    private String degree;

    /**
     * 职称
     */
    private String professionalRanks;

    /**
     * 专业特长
     */
    private String speciality;

    /**
     * 城市代码
     */
    private String city;

    /**
     * 城市呼叫类型
     */
    private String cityCallType;

    /**
     * 县区代码
     */
    private String country;

    /**
     * 地区代码
     */
    private String territoriality;

    /**
     * 是否常委
     */
    private Boolean isStandingCommittee;

    /**
     * 是否监督
     */
    private String isSupervision;

    /**
     * 政协职务
     */
    private String cppccPost;

    /**
     * 所属专委会
     */
    private String belongsSpecialCommittee;

    /**
     * 专委会职务
     */
    private String specialCommitteePost;

    /**
     * 民主党派职务
     */
    private String democraticPartiesPost;

    /**
     * 培训经历
     */
    private String training;

    /**
     * 奖励情况
     */
    private String reward;

    /**
     * 处分情况
     */
    private String punishment;

    /**
     * 社会团体职务
     */
    private String socialOrganizationPost;

    /**
     * 发明创造
     */
    private String invention;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkPhone;

    /**
     * 联系传真
     */
    private String linkTel;

    /**
     * 联系地址
     */
    private String linkAddress;

    /**
     * 联系邮箱
     */
    private String linkMail;

    /**
     * 邮寄地址
     */
    private String mailingAddress;

    /**
     * 身份证号
     */
    private String identificationId;

    /**
     * 简介
     */
    private String introduction;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 专委会职务
     */
    private String specialXommitteePost;

    /**
     * 行政级别
     */
    private String administrativeLevel;

    /**
     * 是否新的社会阶层人士
     */
    private String isPeopleOfTheNewSocialClass;

    /**
     * 外部排序
     */
    private Integer cwSort;

    /**
     * 个人邮箱
     */
    private String myEmail;

    /**
     * 呼叫类型
     */
    private String callType;

    /**
     * 传真
     */
    private String fax;

    /**
     * 其他信息
     */
    private String others;

    /**
     * 关注点
     */
    private String focus;

    /**
     * 专业领域
     */
    private String expertise;

    /**
     * 任职方式
     */
    private String postMode;

    /**
     * 党派小组类型
     */
    private String partyGroupType;

    /**
     * 区域ID
     */
    private String regionId;

    /**
     * 工作时间
     */
    private String timeInWork;

    /**
     * 服务年限
     */
    private String serveTime;

    /**
     * 活动团队职务
     */
    private String activityTeamPost;

    /**
     * 是否自治
     */
    private String isAutonomous;


    /**
     * 逻辑删除标志位
     */
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private String remark;
}