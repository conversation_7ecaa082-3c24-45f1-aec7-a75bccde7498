package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.enums.proposal.HandleWayEnum;
import com.ruoyi.common.enums.proposal.QualityEnum;
import com.ruoyi.common.enums.proposal.UndertakeResultEnum;
import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProposalFeedbackVo {

    @ApiModelProperty(value = "反馈信息id")
    private String id;

    @ApiModelProperty(value = "提案案号")
    private String caseNumber;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "提案人")
    private String proposer;

    @JsonIgnore
    private UndertakeWayEnum undertakeWay;

    @ApiModelProperty(value = "办理结果")
    private UndertakeResultEnum undertakeResult;

    @ApiModelProperty(value = "办理方式")
    private HandleWayEnum handleWay;

    @ApiModelProperty(value = "公开类型")
    private Boolean isOpen;

    @ApiModelProperty(value = "提案质量")
    private QualityEnum proposalQuality;

    @ApiModelProperty(value = "答复内容")
    private String content;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "答复时间")
    private Date createTime;

    @ApiModelProperty(value = "签发领导")
    private String issueUser;

    @ApiModelProperty(value = "承办人员")
    private String undertakePerson;

    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "附件列表")
    List<AnnexVo> replyAnnexList;
}
