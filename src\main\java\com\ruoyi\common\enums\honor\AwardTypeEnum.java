package com.ruoyi.common.enums.honor;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 获奖类型
 * @Param
 * @return
 **/
@Getter
public enum AwardTypeEnum implements IEnum<String> {
    EXCELLENT_MEMBER("1", "优秀政协委员"),
    MORAL_MODEL("2", "道德模范"),
    OTHER("3", "其他");

    @EnumValue
    private final String code;
    private final String label;

    // 静态映射，用于快速查找
    private static final Map<String, AwardTypeEnum> CODE_MAP = new HashMap<>();
    private static final Map<String, AwardTypeEnum> LABEL_MAP = new HashMap<>();

    static {
        for (AwardTypeEnum type : AwardTypeEnum.values()) {
            CODE_MAP.put(type.code, type);
            LABEL_MAP.put(type.label, type);
        }
    }

    AwardTypeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    @Override
    public String getValue() {
        return this.code;
    }

    /**
     * 根据值获取枚举实例
     * @param code 枚举值
     * @return 枚举实例
     */
    public static AwardTypeEnum fromValue(String code) {
        return CODE_MAP.get(code);
    }

    /**
     * 兼容整数类型的查询
     */
    public static AwardTypeEnum fromValue(int code) {
        return fromValue(String.valueOf(code));
    }

    /**
     * 根据标签获取枚举实例
     * @param label 标签名称（如"道德模范"）
     * @return 枚举实例
     */
    public static AwardTypeEnum fromLabel(Object label) {
        if (label instanceof String) {
            return LABEL_MAP.get((String) label);
        }
        return null;
    }
}