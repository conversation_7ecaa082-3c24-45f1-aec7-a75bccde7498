package com.ruoyi.project.community.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.community.domain.ManuscriptFeedback;
import com.ruoyi.project.community.domain.dto.ManuscriptResponseDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ManuscriptFeedbackMapper extends BaseMapper<ManuscriptFeedback> {

    default Boolean existFeedback(String manuscriptId) {
        return exists(new LambdaQueryWrapper<ManuscriptFeedback>()
                .eq(ManuscriptFeedback::getManuscriptId, manuscriptId));
    };

    default ManuscriptFeedback getManuscriptFeedback(String manuscriptId) {
        return selectOne(new LambdaQueryWrapper<ManuscriptFeedback>()
                .eq(ManuscriptFeedback::getManuscriptId, manuscriptId)
        );
    };

    default void updateFeedback(ManuscriptResponseDTO responseDTO) {
        update(null, new LambdaUpdateWrapper<ManuscriptFeedback>()
                .set(ManuscriptFeedback::getFeedback, responseDTO.getText())
                .eq(ManuscriptFeedback::getManuscriptId, responseDTO.getManuscriptId())
        );
    };

    default Map<String, String> selectFeedback(List<String> ids) {
        List<ManuscriptFeedback> manuscriptFeedbacks = selectList(new LambdaQueryWrapper<ManuscriptFeedback>()
                .in(ManuscriptFeedback::getManuscriptId, ids));

        return manuscriptFeedbacks.stream().collect(
                (HashMap<String, String>::new),
                (m, v) -> m.put(v.getManuscriptId(), v.getFeedback()),
                HashMap::putAll
        );
    };

}
