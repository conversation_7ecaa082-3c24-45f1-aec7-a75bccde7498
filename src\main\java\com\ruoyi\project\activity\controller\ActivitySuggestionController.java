package com.ruoyi.project.activity.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySuggestionUpdateDTO;
import com.ruoyi.project.activity.domain.vo.ActivitySuggestionDetailVO;
import com.ruoyi.project.activity.domain.vo.ActivitySuggestionVO;
import com.ruoyi.project.activity.service.IActivitySuggestionService;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 意见建议控制器
 */
@Api(tags = "意见建议管理")
@RestController
@RequestMapping("/activity/suggestion")
public class ActivitySuggestionController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ActivitySuggestionController.class);

    @Autowired
    private IActivitySuggestionService activitySuggestionService;

    @Autowired
    private CommitteeMemberMapper committeeMemberMapper;

    /**
     * 分页查询意见建议列表
     */
    @ApiOperation(value = "分页查询意见建议列表")
    @PreAuthorize("@ss.hasPermi('activity:suggestion:list')")
    @PostMapping("/page")
    public TableDataInfo pageActivitySuggestion(@RequestBody @Validated ActivitySuggestionPageDTO pageDTO) {
        try {
            IPage<ActivitySuggestionVO> page = activitySuggestionService.pageActivitySuggestion(pageDTO);
            return getDataTable(page.getRecords(), page.getTotal());
        } catch (Exception e) {
            log.error("分页查询意见建议失败：", e);
            return getDataTable(null, 0L);
        }
    }

    /**
     * 获取意见建议详情
     */
    @ApiOperation(value = "获取意见建议详情")
    @PreAuthorize("@ss.hasPermi('activity:suggestion:query')")
    @GetMapping("/{id}")
    public AjaxResult getActivitySuggestionDetail(@PathVariable Long id) {
        try {
            ActivitySuggestionDetailVO detail = activitySuggestionService.getActivitySuggestionDetail(id);
            return success(detail);
        } catch (Exception e) {
            log.error("获取意见建议详情失败：", e);
            return error(e.getMessage());
        }
    }

    /**
     * 新增意见建议
     */
    @ApiOperation(value = "新增意见建议")
    @PreAuthorize("@ss.hasPermi('activity:suggestion:add')")
    @Log(title = "意见建议管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addActivitySuggestion(@RequestBody @Validated ActivitySuggestionAddDTO dto) {
        try {
            boolean result = activitySuggestionService.addActivitySuggestion(dto);
            return toAjax(result);
        } catch (Exception e) {
            log.error("新增意见建议失败：", e);
            return error(e.getMessage());
        }
    }

    /**
     * 修改意见建议
     */
    @ApiOperation(value = "修改意见建议")
    @PreAuthorize("@ss.hasPermi('activity:suggestion:edit')")
    @Log(title = "意见建议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult updateActivitySuggestion(@RequestBody @Validated ActivitySuggestionUpdateDTO dto) {
        try {
            boolean result = activitySuggestionService.updateActivitySuggestion(dto);
            return toAjax(result);
        } catch (Exception e) {
            log.error("修改意见建议失败：", e);
            return error(e.getMessage());
        }
    }

    /**
     * 获取当前登录用户的委员ID
     */
    @ApiOperation(value = "获取当前登录用户的委员ID")
    @GetMapping("/currentMemberId")
    public AjaxResult getCurrentMemberId() {
        try {
            String userId = SecurityUtils.getUserId().toString();

            // 查询当前用户是否为委员
            List<CommitteeMember> committeeMemberList = committeeMemberMapper
                    .selectList(new LambdaQueryWrapper<CommitteeMember>()
                            .eq(CommitteeMember::getUserId, userId)
                            .orderByDesc(CommitteeMember::getYear));

            if (committeeMemberList == null || committeeMemberList.isEmpty()) {
                return null;
            }

            // 返回第一个（最新年份的）委员ID
            Long memberId = committeeMemberList.get(0).getId();
            Object resData = String.valueOf(memberId);
            return success(resData);
        } catch (Exception e) {
            log.error("获取当前用户委员ID失败：", e);
            return error("获取委员ID失败：" + e.getMessage());
        }
    }
}
