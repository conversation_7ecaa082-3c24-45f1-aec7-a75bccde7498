package com.ruoyi.project.committee.meeting.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.enums.committee.DocTypeEnum;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@TableName("meeting_doc")
@EqualsAndHashCode(callSuper = true)
public class MeetingDoc extends BaseEntity {

    @TableId
    private Long id;

    /**
     * 会议ID
     */
    private Long meetingId;

    /**
     * 文件标题
     */
    private String title;

    /**
     * 文件正文
     */
    private String content;

    /**
     * 文件类型
     */
    private DocTypeEnum docType;

    /**
     * 是否公开
     */
    private Boolean isPublicly;

    /**
     * 发言人
     */
    private String speaker;

    /**
     * 发言方式
     */
    private String speakType;

    /**
     * 附件
     */
    private String annex;

    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private String remark;
}
