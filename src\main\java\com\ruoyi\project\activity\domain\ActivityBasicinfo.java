package com.ruoyi.project.activity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 活动基本信息实体类
 */
@Data
//@EqualsAndHashCode(callSuper = true)
@TableName("activity_basicinfo")
public class ActivityBasicinfo{

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 原UUID主键
     */
    @TableField("PKID")
    private String pkid;

    /**
     * 活动主题
     */
    @TableField("Title")
    private String title;

    /**
     * 日程安排
     */
    @TableField("Subject")
    private String subject;

    /**
     * 活动类型
     */
    @TableField("Type")
    private String type;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Activity_BeginDate")
    private Date activityBeginDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Activity_EndDate")
    private Date activityEndDate;

    /**
     * 报名开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Enter_BeginDate")
    private Date enterBeginDate;

    /**
     * 报名结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Enter_EndDate")
    private Date enterEndDate;

    /**
     * 活动地址
     */
    @TableField("Address")
    private String address;

    /**
     * 是否获得
     */
    @TableField("Is_Gain")
    private String isGain;

    /**
     * 发布状态
     */
    @TableField("Status")
    private String status;

    /**
     * 创建人ID
     */
    @TableField("Create_ID")
    private String createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Create_Time")
    private Date createTime;

    /**
     * 更新人ID
     */
    @TableField("Update_ID")
    private String updateId;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Update_Time")
    private Date updateTime;

    /**
     * 经度
     */
    @TableField("geo_x")
    private String geoX;

    /**
     * 纬度
     */
    @TableField("geo_y")
    private String geoY;

    /**
     * 半径
     */
    @TableField("radius")
    private String radius;

    /**
     * 活动城市
     */
    @TableField("Activity_City")
    private String activityCity;

    /**
     * 联系人姓名
     */
    @TableField("Link_ManName")
    private String linkManName;

    /**
     * 联系人电话
     */
    @TableField("Link_ManPhone")
    private String linkManPhone;

    /**
     * 是否存在
     */
    @TableField("Is_Exist")
    private String isExist;

    /**
     * 类型详情
     */
    @TableField("type_detail")
    private String typeDetail;

    /**
     * 区域ID
     */
    @TableField("region_id")
    private String regionId;

    /**
     * 补录状态
     */
    @TableField("supplement_status")
    private String supplementStatus;

    /**
     * 发起部门ID
     */
    @TableField(exist = false)
    private Long deptId;
}