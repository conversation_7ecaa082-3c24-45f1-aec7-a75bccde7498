<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalVerifyRecordMapper">
    
    <resultMap type="ProposalVerifyRecord" id="ProposalVerifyRecordResult">
        <result property="id"    column="id"    />
        <result property="proposalId"    column="proposal_id"    />
        <result property="verifierId"    column="verifier_id"    />
        <result property="verifyProcess"    column="verify_process"    />
        <result property="verifyTime"    column="verify_time"    />
        <result property="verifyLog"    column="verify_log"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectProposalVerifyRecordVo">
        select id, proposal_id, verifier_id, verify_process, verify_time, verify_log, del_flag from proposal_verify_record
    </sql>

    <select id="selectProposalVerifyRecordList" parameterType="ProposalVerifyRecord" resultMap="ProposalVerifyRecordResult">
        <include refid="selectProposalVerifyRecordVo"/>
        <where>  
            <if test="proposalId != null "> and proposal_id = #{proposalId}</if>
            <if test="verifierId != null "> and verifier_id = #{verifierId}</if>
            <if test="verifyProcess != null  and verifyProcess != ''"> and verify_process = #{verifyProcess}</if>
            <if test="verifyTime != null "> and verify_time = #{verifyTime}</if>
            <if test="verifyLog != null  and verifyLog != ''"> and verify_log = #{verifyLog}</if>
        </where>
    </select>


    <delete id="deleteProposalVerifyRecordByIds" parameterType="String">
        delete from proposal_verify_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectProposalVerifyRecordByProposalId" resultType="com.ruoyi.project.proposal.domain.vo.ProposalVerifyRecordVo">
        SELECT
            pvr.proposal_id,
            pvr.verifier,
            pvr.verify_process,
            pvr.verify_time,
            pvr.verify_log
        FROM proposal_verify_record pvr
        WHERE pvr.del_flag = false
            AND pvr.proposal_id = #{proposalId}
        ORDER BY pvr.create_time DESC
    </select>
</mapper>