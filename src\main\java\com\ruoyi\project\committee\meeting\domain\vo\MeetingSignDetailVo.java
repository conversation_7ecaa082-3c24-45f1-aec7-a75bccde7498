package com.ruoyi.project.committee.meeting.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MeetingSignDetailVo {

    private String id;

    @ApiModelProperty(value = "委员姓名")
    private String peopleName;

    @ApiModelProperty(value = "签到/请假时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone="GMT+8")
    private Date beginDate;

    @ApiModelProperty(value = "签到类型")
    private String signType;

    @ApiModelProperty(value = "签到类型Label")
    private String signTypeLabel;

    @ApiModelProperty(value = "是否请假")
    private Boolean isLeave;

    @ApiModelProperty(value = "请假理由")
    private String reason;

}
