package com.ruoyi.project.proposal.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.proposal.domain.ProposalHandle;
import com.ruoyi.project.proposal.domain.dto.EvaluationExcelDto;
import com.ruoyi.project.proposal.domain.vo.ProposalHandlePageParamVo;
import com.ruoyi.project.proposal.domain.vo.ProposalHandleEvaPageVo;
import com.ruoyi.project.proposal.domain.vo.ProposalHandlePageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 办理信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Mapper
public interface ProposalHandleMapper extends BaseMapper<ProposalHandle> {

    IPage<ProposalHandleEvaPageVo> selectProposalHandleEvaPage(@Param("page") Page<ProposalHandleEvaPageVo> page,
                                                               @Param("pageParam") ProposalHandlePageParamVo pageParam);

    IPage<ProposalHandlePageVo> selectProposalHandleRevertPage(@Param("page") Page<ProposalHandlePageVo> page,
                                                               @Param("pageParam") ProposalHandlePageParamVo pageParamVo);

    // 办理退回分页查询（承办单位）
    IPage<ProposalHandlePageVo> selectProposalHandleBackPage(@Param("page") Page<ProposalHandlePageVo> page,
                                                             @Param("pageParam") ProposalHandlePageParamVo pageParamVo,
                                                             @Param("ids") Set<Long> ids);

    // 提案办理分页查询（承办单位）
    IPage<ProposalHandlePageVo> selectProposalHandlePage(@Param("page") Page<ProposalHandlePageVo> page,
                                                         @Param("pageParam") ProposalHandlePageParamVo pageParamVo,
                                                         @Param("unitId") Long unitId);

    default ProposalHandle getProposalHandleByProposalId(String proposalId) {
        return selectOne(new LambdaUpdateWrapper<ProposalHandle>()
                .eq(ProposalHandle::getProposalId, proposalId)
        );
    }

    default Boolean existsByProposalId(String proposalId) {
        return exists(new LambdaQueryWrapper<ProposalHandle>()
                .eq(ProposalHandle::getProposalId, proposalId)
        );
    }

    default void updateHandleEvaluationStatus(Long id) {
        LambdaUpdateWrapper<ProposalHandle> updateWrapper = new LambdaUpdateWrapper<ProposalHandle>()
                .set(ProposalHandle::getEvaluationStatus, true)
                .eq(ProposalHandle::getId, id);

        update(null, updateWrapper);
    };

    default int revertProposalHandle(Long handleId) {
        LambdaUpdateWrapper<ProposalHandle> updateWrapper = new LambdaUpdateWrapper<ProposalHandle>()
                .set(ProposalHandle::getRevertStatus, true)
                .eq(ProposalHandle::getId, handleId);
        return update(null, updateWrapper);
    };

    List<String> getOrganizerListByProposalId(@Param("proposalId") String proposalId);

    List<EvaluationExcelDto> selectEvaluationExcelDtoList();

}
