package com.ruoyi.project.committee.archive.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.committee.archive.domain.MemberStatistics;
import com.ruoyi.project.committee.archive.domain.dto.MemberStatisticsPageDto;
import com.ruoyi.project.committee.archive.domain.vo.MemberStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MemberStatisticsMapper extends BaseMapper<MemberStatistics> {

    IPage<MemberStatisticsVo> selectMemberStatisticsPage(@Param("page") Page<MemberStatisticsVo> page,
                                                         @Param("pageDto") MemberStatisticsPageDto pageDto);

    default Boolean existInfo(MemberStatistics memberStatistics) {
        return exists(new LambdaQueryWrapper<MemberStatistics>()
                .eq(ObjectUtil.isNotEmpty(memberStatistics.getUserId()), MemberStatistics::getUserId, memberStatistics.getUserId())
                .eq(ObjectUtil.isNotEmpty(memberStatistics.getYear()), MemberStatistics::getYear, memberStatistics.getYear())
        );
    };

}
