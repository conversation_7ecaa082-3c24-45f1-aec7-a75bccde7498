package com.ruoyi.project.committee.evalrule.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 规则信息DTO
 */
@Data
@ApiModel(value = "规则信息DTO", description = "规则信息数据传输对象")
public class RuleInfoDto {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String pkid;

    /** 年份 */
    @ApiModelProperty(value = "年份")
    private String ruleYear;

    /** 是否可用 */
    @ApiModelProperty(value = "是否可用")
    private Boolean isEnable;

    /** 是否公开 */
    @ApiModelProperty(value = "是否公开")
    private Boolean isPublish;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;
}
