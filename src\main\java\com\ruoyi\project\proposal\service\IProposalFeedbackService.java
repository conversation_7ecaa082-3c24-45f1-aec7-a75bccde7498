package com.ruoyi.project.proposal.service;

import com.ruoyi.project.proposal.domain.dto.ProposalFeedbackDto;
import com.ruoyi.project.proposal.domain.vo.ProposalFeedbackUnitVo;
import com.ruoyi.project.proposal.domain.vo.ProposalReplyFeedbackVo;

public interface IProposalFeedbackService {

    ProposalReplyFeedbackVo selectProposalFeedbackVoById(String proposalId);

    String insertProposalFeedback(ProposalFeedbackDto feedbackDto);

    ProposalFeedbackUnitVo getUnitFeedback(String proposalId);

    Boolean updateProposalFeedbackAnnex(ProposalFeedbackDto feedback);
}
