package com.ruoyi.common.enums.committee;

import lombok.Getter;

/**
 * 会议资料类型枚举
 */
@Getter
public enum DocTypeEnum {

    BRIEF("大会简报"),
    DOCUMENTS("会议文件"),
    WRITTEN_SPEECH("大会发言(书面)"),
    ORAL_SPEECH("大会发言(口头)"),
    REFERENCES("参阅资料"),
    ATTENDANCE_FILES("参会文件"),
    NOTIFICATIONS("会议通知");

    private final String description;

    DocTypeEnum(String description) {
        this.description = description;
    }

}
