package com.ruoyi.project.committee.resumption.domain.query;

import com.ruoyi.common.domain.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 当前用户获奖情况分页查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("当前用户获奖情况分页查询参数")
public class HonorUserPageQuery extends PageQuery {

    private Integer currentPage = 1;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ApiModelProperty("年度")
    private String year;

    @ApiModelProperty("状态，0-暂存，1-提交")
    private String status;
}
