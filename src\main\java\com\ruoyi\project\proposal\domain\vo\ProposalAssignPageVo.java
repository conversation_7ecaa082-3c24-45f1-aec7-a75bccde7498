package com.ruoyi.project.proposal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProposalAssignPageVo {

    private String id;

    @ApiModelProperty(value = "提案年度")
    private Integer year;

    @ApiModelProperty(value = "提案案号")
    private String caseNumber;

    @ApiModelProperty(value = "提案案由")
    private String caseReason;

    @ApiModelProperty(value = "是否交办")
    private Boolean isAssigned;

    @ApiModelProperty(value = "提案者")
    private String proposer;

    @ApiModelProperty(value = "承办单位")
    private String organizer;

}
