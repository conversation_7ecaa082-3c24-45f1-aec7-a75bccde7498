package com.ruoyi.project.committee.resumption.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.committee.resumption.domain.po.ExamineParticipant;
import com.ruoyi.project.committee.resumption.domain.vo.ParticipantVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface ExamineParticipantMapper extends BaseMapper<ExamineParticipant> {

    default void deleteByReportPkId(String reportPkId) {
        delete(new LambdaQueryWrapper<ExamineParticipant>()
                .eq(ExamineParticipant::getReportPkid, reportPkId));
    };

    Set<String> selectReportPkIdByCommittee(@Param("committeeName") String committeeName,
                                                @Param("period") String period,
                                                @Param("rate") String rate,
                                                @Param("userId") String userId,
                                                @Param("belongSpecialCommittee") String belongSpecialCommittee);

    List<ParticipantVo> selectParticipantByReportPkId(@Param("reportPkId") String reportPkId);

    List<ParticipantVo> selectParticipant(List<String> ids);

}
