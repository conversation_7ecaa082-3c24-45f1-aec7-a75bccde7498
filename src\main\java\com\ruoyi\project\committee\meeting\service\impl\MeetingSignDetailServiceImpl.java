package com.ruoyi.project.committee.meeting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.enums.committee.SignTypeEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.security.LoginUser;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.meeting.domain.MeetingInfo;
import com.ruoyi.project.committee.meeting.domain.MeetingSign;
import com.ruoyi.project.committee.meeting.domain.MeetingSignDetail;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignOperateDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingSignDetailVo;
import com.ruoyi.project.committee.meeting.mapper.MeetingMapper;
import com.ruoyi.project.committee.meeting.mapper.MeetingSignDetailMapper;
import com.ruoyi.project.committee.meeting.mapper.MeetingSignMapper;
import com.ruoyi.project.committee.meeting.service.IMeetingSignDetailService;
import com.ruoyi.project.system.domain.SysRole;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 会议签到详情 Service业务层处理
 */
@Service
public class MeetingSignDetailServiceImpl extends ServiceImpl<MeetingSignDetailMapper, MeetingSignDetail> implements IMeetingSignDetailService {

    @Resource
    private MeetingMapper meetingMapper;

    @Resource
    private MeetingSignDetailMapper meetingSignDetailMapper;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    @Override
    public List<MeetingSignDetailVo> getMeetingSignDetailList(String signId) {
        List<MeetingSignDetailVo> detailList = meetingSignDetailMapper.selectMeetingSignDetailList(signId);
        detailList.forEach(detail -> {
            try {
                SignTypeEnum signTypeEnum = SignTypeEnum.valueOf(detail.getSignType());
                detail.setSignType(signTypeEnum.getCode());
                detail.setSignTypeLabel(signTypeEnum.getDescription());
            } catch (Exception e) {
                // Ignore
            }
        });
        return detailList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSignIn(@Valid MeetingSignOperateDto operationDto) {

        if (ObjectUtil.isEmpty(operationDto.getSignDetailIdList())) {
            throw new ServiceException("请选择要签到的委员");
        }

        Integer affectRows = meetingSignDetailMapper.batchSignIn(operationDto);
        return affectRows > 0;
    }

    @Override
    public Boolean signIn(String signDetailId) {

        MeetingSignDetail meetingSignDetail = meetingSignDetailMapper.selectById(signDetailId);
        if (meetingSignDetail == null) {
            throw new ServiceException("该签到信息不存在");
        }


        LambdaUpdateWrapper<MeetingSignDetail> updateWrapper = new LambdaUpdateWrapper<>();
        Long currentUserId = SecurityUtils.getUserId();
        List<SysRole> roleList = SecurityUtils.getLoginUser().getUser().getRoles();
        if (SecurityUtils.isAdmin(roleList)) {
            updateWrapper.set(MeetingSignDetail::getSignType, SignTypeEnum.PROXY);
        }

        boolean exists = committeeMemberMapper.exists(new LambdaQueryWrapper<CommitteeMember>()
                .eq(CommitteeMember::getUserId, currentUserId)
                .eq(CommitteeMember::getId, meetingSignDetail.getPeopleId())
        );
        if (exists) {
            updateWrapper.set(MeetingSignDetail::getSignType, SignTypeEnum.SELF);
        }


        updateWrapper.set(MeetingSignDetail::getIsSign, true);
        updateWrapper.set(MeetingSignDetail::getBeginDate, new Date());
        updateWrapper.eq(MeetingSignDetail::getId, signDetailId);

        int affectRows = meetingSignDetailMapper.update(null, updateWrapper);
        return affectRows > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean leave(@Valid MeetingSignOperateDto operationDto) {
        Integer affectRows = meetingSignDetailMapper.leave(operationDto);
        return affectRows > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(@Valid MeetingSignOperateDto operationDto) {
        Integer affectRows = meetingSignDetailMapper.cancel(operationDto);
        return affectRows > 0;
    }

    @Override
    public void saveBatchSignDetail(List<MeetingSignDetail> signDetailList) {
        if (ObjectUtil.isEmpty(signDetailList)) {
            return;
        }
        saveBatch(signDetailList);
    }
}
