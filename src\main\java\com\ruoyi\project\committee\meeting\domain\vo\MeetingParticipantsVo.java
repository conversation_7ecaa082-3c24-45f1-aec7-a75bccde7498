package com.ruoyi.project.committee.meeting.domain.vo;

import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MeetingParticipantsVo {

    private String id;

    @ApiModelProperty(value = "委员id")
    private String peopleId;

    @Excel(name = "姓名")
    @ApiModelProperty(value = "委员姓名")
    private String peopleName;

    @Excel(name = "职务", width=60)
    @ApiModelProperty(value = "委员职务")
    private String unitPost;

    @ApiModelProperty(value = "是否参加")
    private Boolean isAttend;

    @Excel(name = "是否参加")
    private String isAttendStr;

    @Excel(name = "不参加/请假原因")
    @ApiModelProperty(value = "不参加原因")
    private String noAttendReason;
}
