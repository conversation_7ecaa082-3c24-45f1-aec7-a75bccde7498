package com.ruoyi.project.proposal.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.framework.aspectj.lang.annotation.Anonymous;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.proposal.domain.vo.ProposalReceptionEditVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.project.proposal.domain.ProposalReception;
import com.ruoyi.project.proposal.service.IProposalReceptionService;
import com.ruoyi.common.utils.poi.ExcelUtil;


/**
 * 提案接受情况信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/proposal/reception")
public class ProposalReceptionController extends BaseController {
    @Autowired
    private IProposalReceptionService proposalReceptionService;

    /**
     * 查询提案接收情况信息列表
     */
    @GetMapping("/list")
    public AjaxResult getProposalReceptionList(@RequestParam("proposalId") String proposalId) {
        return AjaxResult.success(proposalReceptionService.selectProposalReceptionList(proposalId));
    }


    /**
     * 获取提案接受情况信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:reception:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(proposalReceptionService.selectProposalReceptionById(id));
    }

    /**
     * 新增提案接收情况信息
     */
    @PreAuthorize("@ss.hasPermi('system:reception:add')")
    @Log(title = "提案接受情况信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProposalReceptionEditVo receptionEditVo) {
        return toAjax(proposalReceptionService.insertProposalReception(receptionEditVo));
    }

    /**
     * 修改提案接收情况信息
     */
    @PreAuthorize("@ss.hasPermi('system:reception:edit')")
    @Log(title = "提案接受情况信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProposalReceptionEditVo receptionEditVo) {
        return toAjax(proposalReceptionService.updateProposalReception(receptionEditVo));
    }

    /**
     * 删除提案接收情况信息
     */
    @PreAuthorize("@ss.hasPermi('system:reception:remove')")
    @Log(title = "提案接收情况信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(proposalReceptionService.deleteProposalReceptionByIds(ids));
    }

    /**
     * 接收提案
     * @param proposalId proposalId
     * @return result
     */
    @GetMapping("/receive")
    public AjaxResult receive(@RequestParam String proposalId) {
        return success(proposalReceptionService.receive(proposalId));
    }
}
