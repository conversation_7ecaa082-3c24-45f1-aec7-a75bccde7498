name: 部署到测试环境
on:
  push:
    branches: [ staging ]  # 根据您的需求调整分支名

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    -
      name: Login docker registry
      uses: docker/login-action@v3
      with:
        registry: registry.cn-hangzhou.aliyuncs.com
        username: ${{ secrets.DOCKER_REGISTRY_USERNAME }}
        password: ${{ secrets.DOCKER_REGISTRY_PASSWORD }}
    -
      name: Build and push
      uses: docker/build-push-action@v6
      with:
        push: true
        tags: registry.cn-hangzhou.aliyuncs.com/toosean/tianya-java:latest
    - 
      name: Deploy to Sit server
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        script: |
          cd /opt/tianya/tianya-java
          sudo docker compose pull
          sudo docker compose down
          sudo docker compose up -d
      timeout-minutes: 2
