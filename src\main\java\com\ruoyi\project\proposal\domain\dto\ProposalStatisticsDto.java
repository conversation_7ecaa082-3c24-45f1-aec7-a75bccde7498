package com.ruoyi.project.proposal.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class ProposalStatisticsDto {

    @ApiModelProperty(value = "统计年度")
    private Integer statisticalYear;

    @ApiModelProperty(value = "性质")
    private String nature;

    @ApiModelProperty(value = "是否接收")
    private Boolean isReceive;

    @ApiModelProperty(value = "Ignore")
    private Set<Long> userIdList;
}
