package com.ruoyi.project.committee.evalrule.service.impl;

import com.ruoyi.common.utils.collections.TripleMap;
import com.ruoyi.project.committee.evalrule.domain.RuleStrategy;
import com.ruoyi.project.committee.evalrule.mapper.RuleStrategyMapper;
import com.ruoyi.project.committee.evalrule.service.IRuleStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@RequiredArgsConstructor
public class RuleStrategyServiceImpl implements IRuleStrategyService {

    private final RuleStrategyMapper ruleStrategyMapper;

    @Override
    public TripleMap<String, String, String> getStrategyMap() {
        TripleMap<String, String, String> strategyMap = new TripleMap<>();
        List<RuleStrategy> strategyList = ruleStrategyMapper.selectStrategyList();

        strategyList.forEach(
                strategy -> strategyMap.put(strategy.getStrategyKey(), strategy.getRuleType(), strategy.getExecuteMethod())
        );

        return strategyMap;
    }
}
