package com.ruoyi.project.activity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 活动通知接收情况VO
 */
@Data
@ApiModel(value = "活动通知接收情况VO")
public class ActivityNotificationReceptionVO {

    /**
     * 通知ID
     */
    @ApiModelProperty(value = "通知ID")
    private String notificationId;

    /**
     * 接收人ID
     */
    @ApiModelProperty(value = "接收人ID")
    private String recipientId;

    /**
     * 接收人姓名
     */
    @ApiModelProperty(value = "接收人姓名")
    private String recipientName;

    /**
     * 接收人委员编号
     */
    @ApiModelProperty(value = "接收人委员编号")
    private String recipientNo;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "接收时间")
    private Date receiveTime;

    /**
     * 接收状态
     */
    @ApiModelProperty(value = "接收状态")
    private Boolean receiveStatus;
}