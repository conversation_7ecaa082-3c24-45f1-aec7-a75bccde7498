package com.ruoyi.common.enums.proposal;

import lombok.Getter;

/**
 * 不立案理由枚举
 */
@Getter
public enum VerifyReasonEnum {
    STATE_SECRET("涉及党和国家秘密的"),
    POLICY_MISMATCH("与党的理论和路线方针政策不相符的"),
    ILLEGAL("国家明令禁止的，与宪法和法律法规相抵触的"),
    PARTY_SUGGEST("中共党员对党内组织、人事安排等方面有意见建议的"),
    GROUP_ISSUE("民主党派或人民团体成员反映本组织内部问题的"),
    LEGAL_PROCEDURE("进入诉讼程序或行政复议、仲裁程序，以及涉及执纪执法机关正在审查的涉嫌违纪违法问题的"),
    REPORT("涉及举报单位和个人的"),
    ACADEMIC("属于学术研讨的"),
    PROMOTE("宣传、推介具体作品、产品的"),
    EXCEED_SCOPE("超出人民政协履职范围，对党政机关及其工作人员要求进行质询或追责的"),
    OUT_JURISDICTION("超出本省管辖权范围的"),
    TOO_BROAD("内容过于宏观，建议涉及党政工作多个方面或在现有条件下无法得到落实"),
    REQ_INSTITUTION("单纯要求增设机构、增加编制或为具体项目申请立项、经费的"),
    DEPT_ISSUE("属于部门工作内容或下级组织向上级组织反应问题的"),
    FORWARD_LETTER("代转他人来信，要求解决部门或个人问题的"),
    PERSONAL_VIEW("对一些问题或现象表达个人看法和观点，没有可操作具体建议的"),
    RESOLVED("问题已经得到解决，所提建议滞后的"),
    OTHER("其他不宜作为提案办理的");

    private final String description;

    VerifyReasonEnum(String description) {
        this.description = description;
    }
}
