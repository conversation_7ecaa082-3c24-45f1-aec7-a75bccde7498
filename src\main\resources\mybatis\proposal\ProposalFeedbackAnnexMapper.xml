<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalFeedbackAnnexMapper">

    <select id="selectFeedbackAnnexList" resultType="com.ruoyi.project.proposal.domain.vo.AnnexVo">
        SELECT
            a.id,
            a.url,
            a.annex_name,
            a.annex_type
        FROM proposal_feedback_annex pfa
            LEFT JOIN annex a ON a.id = pfa.annex_id
        WHERE pfa.feedback_id = #{id}

    </select>
</mapper>