package com.ruoyi.project.committee.resumption.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.committee.resumption.domain.po.CommunityAnnex;
import com.ruoyi.project.committee.resumption.mapper.CommunityAnnexMapper;
import com.ruoyi.project.committee.resumption.service.ICommunityAnnexService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公益情况附件Service实现
 */
@Service
@RequiredArgsConstructor
public class CommunityAnnexServiceImpl extends ServiceImpl<CommunityAnnexMapper, CommunityAnnex> implements ICommunityAnnexService {

    @Override
    public List<CommunityAnnex> selectListByCommunityId(String communityId) {
        return baseMapper.selectListByCommunityId(communityId);
    }

    @Override
    public boolean deleteByCommunityId(String communityId) {
        LambdaQueryWrapper<CommunityAnnex> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommunityAnnex::getCommunityId, communityId);
        return remove(wrapper);
    }
}
