package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 当前用户活动签到明细查询DTO
 */
@Data
@ApiModel(value = "当前用户活动签到明细查询DTO")
public class UpdateActivitySignDetailDTO {

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityPkid;

    /**
     * 签到状态
     */
    @ApiModelProperty(value = "签到状态", required = false)
    private String status;

    /**
     * 签到说明
     */
    @ApiModelProperty(value = "签到说明", required = false)
    private String reason;
    

} 