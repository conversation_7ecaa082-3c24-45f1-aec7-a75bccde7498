package com.ruoyi.project.committee.meeting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingDocEditDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingDocPageDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingDocPageVo;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingDocVo;

public interface IMeetingDocService {

    Long saveMeetingDoc(MeetingDocEditDto createDto);

    Boolean updateMeetingDoc(MeetingDocEditDto updateDto);

    Boolean deleteMeetingDoc(String id);

    IPage<MeetingDocPageVo> getMeetingDocPage(MeetingDocPageDto pageDto);

    MeetingDocVo getMeetingDoc(String id);
}
