-- MySQL dump 10.13  Distrib 8.0.33, for Win64 (x86_64)
--
-- Host: localhost    Database: ry-vue
-- ------------------------------------------------------
-- Server version	8.0.33

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `proposal_handle_organizer`
--

DROP TABLE IF EXISTS `proposal_handle_organizer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_handle_organizer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `handle_id` bigint DEFAULT NULL COMMENT '办理id',
  `dept_id` bigint DEFAULT NULL COMMENT '部门id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案办理承办单位关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_verify_record`
--

DROP TABLE IF EXISTS `proposal_verify_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_verify_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `proposal_id` bigint DEFAULT NULL COMMENT '提案id',
  `verifier_id` bigint DEFAULT NULL COMMENT '审核人id',
  `verify_process` varchar(20) DEFAULT NULL COMMENT '审核环节',
  `verify_time` date DEFAULT NULL COMMENT '审核时间',
  `verify_log` varchar(300) DEFAULT NULL COMMENT '审核日志',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案审核记录信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_handle_rel`
--

DROP TABLE IF EXISTS `proposal_handle_rel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_handle_rel` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `proposal_id` bigint DEFAULT NULL COMMENT '提案id',
  `handle_id` bigint DEFAULT NULL COMMENT '办理信息id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案办理信息关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_feedback`
--

DROP TABLE IF EXISTS `proposal_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_feedback` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `proposal_id` bigint DEFAULT NULL COMMENT '提案id',
  `dept_id` bigint DEFAULT NULL COMMENT '部门id',
  `title` varchar(100) DEFAULT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '内容',
  `issue_date` date DEFAULT NULL COMMENT '签发日期',
  `issue_user` bigint DEFAULT NULL COMMENT '签发人',
  `create_by` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案答复及反馈信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_quality_evaluation`
--

DROP TABLE IF EXISTS `proposal_quality_evaluation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_quality_evaluation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proposal_id` bigint DEFAULT NULL COMMENT '提案id',
  `dept_id` bigint DEFAULT NULL COMMENT '单位id',
  `evaluation` varchar(100) DEFAULT NULL COMMENT '总体评价',
  `evaluation_time` date DEFAULT NULL COMMENT '评价时间',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='质量评价信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_annex_rel`
--

DROP TABLE IF EXISTS `proposal_annex_rel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_annex_rel` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `proposal_id` bigint DEFAULT NULL COMMENT '提案id',
  `annex_id` bigint DEFAULT NULL COMMENT '附件id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案附件关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_reception`
--

DROP TABLE IF EXISTS `proposal_reception`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_reception` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proposal_id` bigint DEFAULT NULL COMMENT '提案id',
  `dept_id` bigint DEFAULT NULL COMMENT '单位id',
  `join_time` date DEFAULT NULL COMMENT '交办日期',
  `receive_time` date DEFAULT NULL COMMENT '接收时间',
  `receive_status` tinyint(1) DEFAULT '0' COMMENT '接收状态',
  `undertake_way` varchar(100) DEFAULT NULL COMMENT '承办方式',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案接受情况信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal`
--

DROP TABLE IF EXISTS `proposal`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `year` int DEFAULT NULL COMMENT '提案年度',
  `serial_number` int DEFAULT NULL COMMENT '流水号',
  `case_number` int DEFAULT NULL COMMENT '提案案号',
  `case_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '提案类别(经济,政治,文化,社会,生态文明,其他)',
  `case_category` varchar(10) DEFAULT NULL COMMENT '提案案别',
  `case_reason` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '提案案由',
  `case_content` text COMMENT '提案内容',
  `proposer` varchar(150) DEFAULT NULL COMMENT '提案者',
  `instructions` varchar(100) DEFAULT NULL COMMENT '相关情况',
  `remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注内容',
  `register_date` datetime DEFAULT NULL COMMENT '登记时间',
  `submit_type` varchar(10) DEFAULT NULL COMMENT '提交类型(个人，个人联名，集体)',
  `case_filing` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '提案状态(立案，已撤案，不立案，已并案，待立案，带办理， 办结)',
  `retransact_status` varchar(10) DEFAULT NULL COMMENT '改办状态',
  `track` varchar(100) DEFAULT NULL COMMENT '跟踪情况',
  `is_open` tinyint(1) DEFAULT '0' COMMENT '是否公开',
  `is_final_trial` tinyint(1) DEFAULT '0' COMMENT '是否终审',
  `is_assigned` tinyint(1) DEFAULT '0' COMMENT '是否交办',
  `is_received` tinyint(1) DEFAULT '0' COMMENT '是否接收',
  `is_finished` tinyint(1) DEFAULT '0' COMMENT '是否办结',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案信息';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `before_Insert` BEFORE INSERT ON `proposal` FOR EACH ROW BEGIN
  SET NEW.serial_number = (SELECT IFNULL(MAX(serial_number), 0) + 1 FROM proposal);
  SET NEW.case_number = (SELECT IFNULL(MAX(case_number), 0) + 1 FROM proposal);
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `proposal_feedback_annex`
--

DROP TABLE IF EXISTS `proposal_feedback_annex`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_feedback_annex` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `feedback_id` bigint DEFAULT NULL COMMENT '提案id',
  `annex_id` bigint DEFAULT NULL COMMENT '附件id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案答复及反馈附件关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_user_rel`
--

DROP TABLE IF EXISTS `proposal_user_rel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_user_rel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `proposal_id` bigint NOT NULL COMMENT '提案id',
  `proposer_id` bigint NOT NULL COMMENT '提案者id',
  `submit_type` varchar(10) DEFAULT NULL COMMENT '提交类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案人员关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `annex`
--

DROP TABLE IF EXISTS `annex`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `annex` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '附件url',
  `annex_name` varchar(100) DEFAULT NULL COMMENT '附件名称',
  `annex_type` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '附件类型',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='附件信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_handle`
--

DROP TABLE IF EXISTS `proposal_handle`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_handle` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `organizers` bigint DEFAULT NULL COMMENT '承办单位',
  `undertake_result` varchar(100) DEFAULT NULL COMMENT '处理结果',
  `undertake_person` bigint DEFAULT NULL COMMENT '承办人员',
  `undertake_way` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '承办方式',
  `proposal_quality` varchar(50) DEFAULT NULL COMMENT '提案质量',
  `handle_way` varchar(100) DEFAULT NULL COMMENT '办理方式',
  `undertake_time` date DEFAULT NULL COMMENT '办结日期',
  `evaluation_status` tinyint(1) DEFAULT '0' COMMENT '评价状态(0-未评价,1-已评价)',
  `feedback_status` tinyint(1) DEFAULT '0' COMMENT '反馈状态(0-未反馈,1-已反馈)',
  `revert_status` tinyint(1) DEFAULT '0' COMMENT '退回状态(0-未退回,1-已退回)',
  `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='办理信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proposal_handle_evaluations`
--

DROP TABLE IF EXISTS `proposal_handle_evaluations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proposal_handle_evaluations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `handle_id` bigint DEFAULT NULL COMMENT '办理id',
  `is_timely_response` tinyint(1) DEFAULT '0' COMMENT '是否按时对提案进行答复',
  `has_formal_report` tinyint(1) DEFAULT '0' COMMENT '是否收到正式办理报告',
  `has_dedicated_handler` tinyint(1) DEFAULT '0' COMMENT '是否有专门办理人员',
  `is_leaders_signature` tinyint(1) DEFAULT '0' COMMENT '办理报告是否有领导签发',
  `has_pre_post_negotiation` tinyint(1) DEFAULT '0' COMMENT '是否有办前、办中、办后协商',
  `is_standard_handover` tinyint(1) DEFAULT '0' COMMENT '交办是否标准',
  `adopt_or_partially_adopt` tinyint(1) DEFAULT '0' COMMENT '采纳或部分采纳建议',
  `need_to_implement` tinyint(1) DEFAULT '0' COMMENT '有解决方案需跟踪落实',
  `solved_or_partially_solved` tinyint(1) DEFAULT '0' COMMENT '已解决或基本解决',
  `no_solution` tinyint(1) DEFAULT '0' COMMENT '无解决方案',
  `explained_clearly` tinyint(1) DEFAULT '0' COMMENT '向提案者解释清楚',
  `not_explained` tinyint(1) DEFAULT '0' COMMENT '未向提案者解悉情况',
  `other_opinions` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '其他意见',
  `business_ability` int DEFAULT '0' COMMENT '承办业务能力',
  `active_communication` int DEFAULT '0' COMMENT '沟通的主动性',
  `sufficient_exchange` int DEFAULT '0' COMMENT '交流的充分性',
  `targeted_processing` int DEFAULT '0' COMMENT '办理的针对性',
  `consensus_on_problems` int DEFAULT '0' COMMENT '对问题的共识度',
  `overall_evaluation` decimal(10,1) DEFAULT '0.0' COMMENT '对办理的总体评价',
  `create_by` varchar(10) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(10) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标识位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案办理情况评价表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-09-04 12:18:18
-- `ry-vue`.proposal_supervision_record definition

CREATE TABLE `proposal_supervision_record` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `proposal_id` varchar(100) DEFAULT NULL,
    `contact` varchar(10) DEFAULT NULL COMMENT '联系人',
    `contact_phone` varchar(11) DEFAULT NULL COMMENT '联系人电话',
    `create_time` datetime DEFAULT NULL COMMENT '时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='提案督察记录';
-- `ry-vue`.proposal_supervision_record definition

-- `ry-vue`.issue_feedback definition

CREATE TABLE `issue_feedback` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `content` text COMMENT '反馈内容',
    `feedback` text COMMENT '问题回复',
    `create_by` varchar(20) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(20) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='问题反馈';
