package com.ruoyi.project.committee.meeting.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.committee.meeting.domain.MeetingSign;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignPageDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingSignPageVo;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingSignVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会议签到信息 Mapper 接口
 */
@Mapper
public interface MeetingSignMapper extends BaseMapper<MeetingSign> {

    default IPage<MeetingSign> getMeetingSignPage(Page<MeetingSign> page, MeetingSignPageDto pageDto) {
        return selectPage(page, new LambdaQueryWrapper<MeetingSign>()
                .eq(MeetingSign::getMeetingId, pageDto.getMeetingId())
                .like(ObjectUtil.isNotEmpty(pageDto.getReason()), MeetingSign::getReason, pageDto.getReason())
                .orderByDesc(MeetingSign::getCreateTime)
        );
    };

    IPage<MeetingSignPageVo> getMyMeetingSignPage(@Param("page") Page<MeetingSignPageVo> page,
                                                  @Param("pageDto") MeetingSignPageDto pageDto,
                                                  @Param("currentUserId") Long currentUserId);
}
