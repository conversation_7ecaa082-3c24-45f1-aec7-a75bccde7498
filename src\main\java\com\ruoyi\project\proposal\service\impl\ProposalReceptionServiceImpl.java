package com.ruoyi.project.proposal.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.project.proposal.converter.ReceptionConverter;
import com.ruoyi.project.proposal.domain.ProposalUndertakeUnit;
import com.ruoyi.project.proposal.domain.dto.UndertakeUnitDto;
import com.ruoyi.project.proposal.domain.vo.ProposalReceptionEditVo;
import com.ruoyi.project.proposal.domain.vo.ProposalReceptionVo;
import com.ruoyi.project.proposal.mapper.ProposalUndertakeUnitMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.proposal.mapper.ProposalReceptionMapper;
import com.ruoyi.project.proposal.domain.ProposalReception;
import com.ruoyi.project.proposal.service.IProposalReceptionService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 提案接受情况信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Service
public class ProposalReceptionServiceImpl implements IProposalReceptionService {

    @Autowired
    private ProposalReceptionMapper proposalReceptionMapper;

    @Autowired
    private ProposalUndertakeUnitMapper undertakeUnitMapper;

    /**
     * 查询提案接受情况信息
     * 
     * @param id 提案接受情况信息主键
     * @return 提案接受情况信息
     */
    @Override
    public ProposalReception selectProposalReceptionById(Long id) {
        return proposalReceptionMapper.selectById(id);
    }

    /**
     * 查询提案接受情况信息列表
     *
     * @param proposalId 提案id
     * @return 提案接受情况信息
     */
    @Override
    public List<ProposalReceptionVo> selectProposalReceptionList(String proposalId) {
        return proposalReceptionMapper.selectProposalReceptionList(proposalId);
    }

    /**
     * 新增提案接受情况信息
     * 
     * @param receptionEditVo 提案接受情况信息
     * @return 结果
     */
    @Override
    public int insertProposalReception(ProposalReceptionEditVo receptionEditVo) {
        ProposalReception proposalReception = ReceptionConverter.INSTANCE.convert(receptionEditVo);
        proposalReception.setReceiveTime(new Date(System.currentTimeMillis()));
        return proposalReceptionMapper.insert(proposalReception);
    }

    /**
     * 修改提案接受情况信息
     * 
     * @param receptionEditVo 提案接受情况信息
     * @return 结果
     */
    @Override
    public int updateProposalReception(ProposalReceptionEditVo receptionEditVo) {
        ProposalReception proposalReception = ReceptionConverter.INSTANCE.convert(receptionEditVo);
        return proposalReceptionMapper.updateById(proposalReception);
    }

    /**
     * 批量删除提案接受情况信息
     * 
     * @param ids 需要删除的提案接受情况信息主键
     * @return 结果
     */
    @Override
    public int deleteProposalReceptionByIds(Long[] ids)
    {
        return proposalReceptionMapper.deleteProposalReceptionByIds(ids);
    }

    /**
     * 删除提案接受情况信息信息
     * 
     * @param id 提案接受情况信息主键
     * @return 结果
     */
    @Override
    public int deleteProposalReceptionById(Long id)
    {
        return proposalReceptionMapper.deleteById(id);
    }

    @Override
    public void addReception(String proposalId, List<UndertakeUnitDto> undertakeUnits) {
        if (ObjectUtil.isEmpty(undertakeUnits)) {
            return;
        }

        undertakeUnits.forEach(undertakeUnitDto -> {
            ProposalReception proposalReception = new ProposalReception();
            proposalReception.setProposalId(proposalId);
            proposalReception.setRecipientId(Long.valueOf(undertakeUnitDto.getUnitId()));
            proposalReception.setJoinTime(new Date());
            proposalReception.setUndertakeWay(undertakeUnitDto.getUndertakeWay().name());

            proposalReceptionMapper.insert(proposalReception);
        });
    }

    @Override
    public Boolean receive(String proposalId) {
        Long userId = SecurityUtils.getUserId();
        if (!proposalReceptionMapper.existUserReception(proposalId, userId)) {
            throw new ServiceException("您不是该提案的接收人，无法操作");
        }

        proposalReceptionMapper.receive(proposalId, userId);

        return true;
    }

    @Override
    public Map<String, List<ProposalReceptionVo>> selectReceptionByIds(List<String> idList) {
        if (ObjectUtil.isNotEmpty(idList)) {
            List<ProposalReceptionVo> dataList = proposalReceptionMapper.selectReceptionByProposalIds(idList);
            return dataList.stream().collect(Collectors.groupingBy(ProposalReceptionVo::getProposalId));
        }

        return Collections.emptyMap();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(String proposalId, List<UndertakeUnitDto> undertakeUnits) {

        List<ProposalUndertakeUnit> unitList = undertakeUnitMapper.selectUndertakeUnitList(proposalId);
        if (ObjectUtil.isNotEmpty(unitList)) {
            List<Long> unHandleUnitIds = unitList.stream()
                    .filter(item -> !item.getHandleStatus())
                    .map(ProposalUndertakeUnit::getUnitId)
                    .collect(Collectors.toList());

            List<Long> alreadyHandleUnitIds = unitList.stream()
                    .filter(ProposalUndertakeUnit::getHandleStatus)
                    .map(ProposalUndertakeUnit::getUnitId)
                    .collect(Collectors.toList());

            // 删除未办理的接收情况
            if (ObjectUtil.isNotEmpty(unHandleUnitIds)) {
                if (ObjectUtil.isNotEmpty(alreadyHandleUnitIds)) {
                    proposalReceptionMapper.delete(new LambdaQueryWrapper<ProposalReception>()
                            .eq(ProposalReception::getProposalId, proposalId)
                            .notIn(ProposalReception::getRecipientId, alreadyHandleUnitIds)
                    );
                } else {
                    proposalReceptionMapper.delete(new LambdaQueryWrapper<ProposalReception>()
                            .eq(ProposalReception::getProposalId, proposalId)
                    );
                }

                // 新增接收情况
                if (ObjectUtil.isNotEmpty(undertakeUnits)) {
                    for (UndertakeUnitDto unit : undertakeUnits) {
                        if (unHandleUnitIds.contains(Long.parseLong(unit.getUnitId()))) {
                            ProposalReception reception = new ProposalReception();
                            reception.setProposalId(proposalId);
                            reception.setRecipientId(Long.parseLong(unit.getUnitId()));
                            reception.setJoinTime(new Date());
                            reception.setReceiveStatus(false);
                            reception.setUndertakeWay(unit.getUndertakeWay().name());
                            proposalReceptionMapper.insert(reception);

                        }
                    }
                }
            }

        }

    }
}
