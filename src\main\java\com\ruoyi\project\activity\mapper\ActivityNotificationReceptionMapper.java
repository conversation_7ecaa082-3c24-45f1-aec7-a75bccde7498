package com.ruoyi.project.activity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.activity.domain.ActivityNotificationReception;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationReceptionPageDTO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationReceptionVO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationVO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动通知接收情况数据层
 */
@Mapper
public interface ActivityNotificationReceptionMapper extends BaseMapper<ActivityNotificationReception> {

        /**
         * 分页查询活动通知接收情况信息列表
         *
         * @param page      分页参数
         * @param searchDto 查询参数
         * @return 活动通知接收情况分页列表
         */
        IPage<ActivityNotificationReceptionVO> selectNotificationReceptionPage(
                        @Param("page") Page<ActivityNotificationReceptionVO> page,
                        @Param("searchDto") ActivityNotificationReceptionPageDTO searchDto);

        /**
         * 批量删除活动通知接收情况
         * 
         * @param notificationIds 通知ID列表
         * @param updateBy        更新人ID
         * @return 影响行数
         */
        int deleteByNotificationIds(@Param("notificationIds") List<Long> notificationIds,
                        @Param("updateBy") Long updateBy);

        /**
         * 批量插入活动通知接收情况
         * 
         * @param receptions 接收情况列表
         * @return 影响行数
         */
        int saveOrUpdateBatch(@Param("entities") List<ActivityNotificationReception> receptions);

        /**
         * 根据通知ID查询接收情况列表
         *
         * @param notificationId 通知ID
         * @return 接收情况列表
         */
        List<ActivityNotificationReceptionVO> selectReceptionListByNotificationId(
                        @Param("notificationId") Long notificationId);

        /**
         * 根据通知ID批量逻辑删除接收情况
         *
         * @param notificationId 通知ID
         * @param updateBy       更新人ID
         * @return 影响行数
         */
        int logicDeleteByNotificationId(@Param("notificationId") Long notificationId, @Param("updateBy") Long updateBy);

        /**
         * 根据通知ID和接收人ID列表批量更新接收状态为未接收
         *
         * @param notificationId 通知ID
         * @param recipientIds   接收人ID列表
         * @param updateBy       更新人ID
         * @return 影响行数
         */
        int resetReceiveStatusByNotificationIdAndRecipientIds(
                        @Param("notificationId") Long notificationId,
                        @Param("recipientIds") List<Long> recipientIds,
                        @Param("updateBy") Long updateBy);

        /**
         * 根据活动ID查询所有通知ID
         *
         * @param activityId 活动ID
         * @return 通知ID列表
         */
        List<Long> selectNotificationIdsByActivityId(@Param("activityId") Long activityId);

        /**
         * 根据通知ID和接收人ID列表批量恢复逻辑删除的记录
         *
         * @param notificationId 通知ID
         * @param recipientIds   接收人ID列表
         * @param updateBy       更新人ID
         * @return 影响行数
         */
        int restoreDeletedByNotificationIdAndRecipientIds(
                        @Param("notificationId") Long notificationId,
                        @Param("recipientIds") List<Long> recipientIds,
                        @Param("updateBy") Long updateBy);

        /**
         * 根据通知ID和接收人ID列表批量逻辑删除记录
         *
         * @param notificationId 通知ID
         * @param recipientIds   接收人ID列表
         * @param updateBy       更新人ID
         * @return 影响行数
         */
        int logicDeleteByNotificationIdAndRecipientIds(
                        @Param("notificationId") Long notificationId,
                        @Param("recipientIds") List<Long> recipientIds,
                        @Param("updateBy") Long updateBy);
}