package com.ruoyi.project.activity.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.activity.domain.ActivityNotification;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationPageDTO;
import com.ruoyi.project.activity.domain.dto.ActivityNotificationUpdateDTO;
import com.ruoyi.project.activity.domain.vo.ActivityNotificationVO;
import com.ruoyi.project.activity.mapper.ActivityNotificationMapper;
import com.ruoyi.project.activity.mapper.ActivityNotificationReceptionMapper;
import com.ruoyi.project.activity.service.IActivityNotificationReceptionService;
import com.ruoyi.project.activity.service.IActivityNotificationService;

import cn.hutool.core.util.ObjectUtil;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

import javax.annotation.Resource;

/**
 * 活动通知服务实现类
 */
@Service
public class ActivityNotificationServiceImpl extends ServiceImpl<ActivityNotificationMapper, ActivityNotification>
        implements IActivityNotificationService {

    @Resource
    private ActivityNotificationMapper activityNotificationMapper;

    @Resource
    private ActivityNotificationReceptionMapper activityNotificationReceptionMapper;

    @Autowired
    private IActivityNotificationReceptionService activityNotificationReceptionService;

    /**
     * 分页查询活动通知列表
     *
     * @param dto 查询参数
     * @return 活动通知分页列表
     */
    @Override
    public IPage<ActivityNotificationVO> getNotificationPage(ActivityNotificationPageDTO searchDto) {
        // 设置页码
        searchDto.setPageNo(searchDto.getCurrentPage());

        // 创建分页对象
        Page<ActivityNotificationVO> page = new Page<>(searchDto.getCurrentPage(), searchDto.getPageSize());

        // 查询数据
        IPage<ActivityNotificationVO> result = activityNotificationMapper.selectNotificationPage(page, searchDto);

        return result;
    }

    /**
     * 根据ID获取活动通知详情
     *
     * @param id 通知ID
     * @return 活动通知详情
     */
    @Override
    public ActivityNotificationVO getNotificationById(String id) {
        // 查询通知基本信息
        ActivityNotification notification = this.getById(id);

        if (ObjectUtil.isNull(notification)) {
            throw new ServiceException("活动通知不存在");
        }

        return activityNotificationMapper.selectDetailById(id);
    }

    /**
     * 新增活动通知
     *
     * @param dto 新增参数
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addNotification(ActivityNotificationAddDTO dto) {
        ActivityNotification notification = new ActivityNotification();
        BeanUtils.copyProperties(dto, notification);

        // 如果没有设置发布者的话，使用接口调用用户的userId
        if (StringUtils.isEmpty(dto.getPublisherId())) {
            notification.setCreateBy(SecurityUtils.getUserId());
        } else {
            notification.setCreateBy(Long.valueOf(dto.getPublisherId()));
        }

        // 设置创建人和创建时间
        notification.setCreateAt(new Date());

        // 设置删除标志
        notification.setDelFlag(false);

        int notificationId = activityNotificationMapper.insert(notification);

        if (notificationId > 0) {
            // 插入活动通知接收情况列表
            return activityNotificationReceptionService.batchCreateNotificationReceptions(
                    Long.valueOf(dto.getActivityId()),
                    Long.valueOf(notification.getId()));
        }

        return false;
    }

    /**
     * 更新活动通知
     *
     * @param dto 更新参数
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotification(ActivityNotificationUpdateDTO dto) {
        // 查询原通知
        ActivityNotification notification = this.getById(dto.getId());
        if (ObjectUtil.isNull(notification)) {
            throw new ServiceException("活动通知不存在");
        }

        // 判读字段是否是null或者是空字符串
        if (!StringUtils.isEmpty(dto.getTitle())) {
            notification.setTitle(dto.getTitle());
        }
        if (!StringUtils.isEmpty(dto.getContent())) {
            notification.setContent(dto.getContent());
        }
        if (dto.getIsSendSms() != null) {
            notification.setIsSendSms(dto.getIsSendSms());
        }

        // 设置更新人和更新时间
        notification.setUpdateBy(SecurityUtils.getUserId());
        notification.setUpdateAt(new Date());

        return activityNotificationMapper.updateById(notification) > 0;
    }

    /**
     * 删除活动通知
     *
     * @param id 通知ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotification(String id) {
        ActivityNotification notification = new ActivityNotification();

        if (ObjectUtil.isNull(notification)) {
            throw new ServiceException("活动通知不存在");
        }

        notification.setId(id);
        notification.setDelFlag(true);
        notification.setUpdateBy(SecurityUtils.getUserId());
        notification.setUpdateAt(new Date());

        // 删除通知
        int delRes = activityNotificationMapper.updateById(notification);

        if (delRes > 0) {
            // 删除活动通知接收情况
            return activityNotificationReceptionService.deleteNotificationReceptionsByNotificationId(Long.valueOf(id));
        }

        return false;
    }
}