package com.ruoyi.project.proposal.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ProposalHandleStatisticsVo {
    @ApiModelProperty(value = "收到提案")
    private Long receivedProposals;

    @ApiModelProperty(value = "立案件数")
    private Long filedProposals;

    @ApiModelProperty(value = "单独办理")
    private Long individuallyProcessed;

    @ApiModelProperty(value = "分别办理")
    private Long separatelyProcessed;

    @ApiModelProperty(value =  "解决程度")
    private String solutionDegree;

    @ApiModelProperty(value = "已办结提案的承办单位")
    private List<String> completedUnits;

    @ApiModelProperty(value = "正在办理的承办单位")
    private List<String> processingUnits;

    @ApiModelProperty(value = "尚未开始办理的单位")
    private List<String> notStartedUnitNames;

}
