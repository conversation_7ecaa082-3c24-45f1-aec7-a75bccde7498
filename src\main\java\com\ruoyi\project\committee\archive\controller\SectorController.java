package com.ruoyi.project.committee.archive.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.committee.archive.service.ISectorService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 界别信息
 */
@RestController
@RequestMapping(value = "/sector")
public class SectorController extends BaseController {

    @Resource
    private ISectorService sectorService;

    /**
     * 获取界别信息列表
     * @return result
     */
    @GetMapping("/list")
    public TableDataInfo getSectorList() {
        return getDataTable(sectorService.selectSectorList());
    }
}
