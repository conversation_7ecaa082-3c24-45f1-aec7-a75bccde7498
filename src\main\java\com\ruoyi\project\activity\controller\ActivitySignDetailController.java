package com.ruoyi.project.activity.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.activity.domain.ActivitySignDetail;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailBatchDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailLeaveDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignDetailPageDTO;
import com.ruoyi.project.activity.domain.dto.UpdateActivitySignDetailDTO;
import com.ruoyi.project.activity.domain.dto.UserActivitySignDetailDTO;
import com.ruoyi.project.activity.domain.dto.UserUpdateActivitySignDetailStatusDTO;
import com.ruoyi.project.activity.domain.vo.ActivitySignDetailVO;
import com.ruoyi.project.activity.domain.vo.UserActivitySignDetailVO;
import com.ruoyi.project.activity.service.IActivitySignDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 活动签到明细控制器
 */
@Api(tags = "活动签到明细管理")
@RestController
@RequestMapping("/activity/signdetail")
public class ActivitySignDetailController extends BaseController {

    @Autowired
    private IActivitySignDetailService activitySignDetailService;

    /**
     * 获取签到明细列表
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(ActivitySignDetail activitySignDetail) {
        startPage();
        LambdaQueryWrapper<ActivitySignDetail> queryWrapper = new LambdaQueryWrapper<>();

        if (activitySignDetail.getSignPkid() != null && !activitySignDetail.getSignPkid().isEmpty()) {
            queryWrapper.eq(ActivitySignDetail::getSignPkid, activitySignDetail.getSignPkid());
        }

        if (activitySignDetail.getPepolePkid() != null && !activitySignDetail.getPepolePkid().isEmpty()) {
            queryWrapper.eq(ActivitySignDetail::getPepolePkid, activitySignDetail.getPepolePkid());
        }

        if (activitySignDetail.getActivityPkid() != null && !activitySignDetail.getActivityPkid().isEmpty()) {
            queryWrapper.eq(ActivitySignDetail::getActivityPkid, activitySignDetail.getActivityPkid());
        }

        if (activitySignDetail.getIsSign() != null && !activitySignDetail.getIsSign().isEmpty()) {
            queryWrapper.eq(ActivitySignDetail::getIsSign, activitySignDetail.getIsSign());
        }

        // 按开始时间降序排列
        queryWrapper.orderByDesc(ActivitySignDetail::getBeginDate);

        List<ActivitySignDetail> list = activitySignDetailService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 获取签到明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return success(activitySignDetailService.getById(id));
    }

    /**
     * 获取签到记录的所有明细
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:list')")
    @GetMapping("/sign/{signPkid}")
    public AjaxResult getDetailsBySign(@PathVariable String signPkid) {
        LambdaQueryWrapper<ActivitySignDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySignDetail::getSignPkid, signPkid);
        queryWrapper.orderByDesc(ActivitySignDetail::getBeginDate);
        return success(activitySignDetailService.list(queryWrapper));
    }

    /**
     * 获取某个活动的所有签到明细
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:list')")
    @GetMapping("/activity/{activityPkid}")
    public AjaxResult getDetailsByActivity(@PathVariable String activityPkid) {
        LambdaQueryWrapper<ActivitySignDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySignDetail::getActivityPkid, activityPkid);
        queryWrapper.orderByDesc(ActivitySignDetail::getBeginDate);
        return success(activitySignDetailService.list(queryWrapper));
    }

    /**
     * 获取某个人员的所有签到明细
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:list')")
    @GetMapping("/people/{pepolePkid}")
    public AjaxResult getDetailsByPeople(@PathVariable String pepolePkid) {
        LambdaQueryWrapper<ActivitySignDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySignDetail::getPepolePkid, pepolePkid);
        queryWrapper.orderByDesc(ActivitySignDetail::getBeginDate);
        return success(activitySignDetailService.list(queryWrapper));
    }

    /**
     * 新增签到明细
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:add')")
    @Log(title = "签到明细管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ActivitySignDetail activitySignDetail) {
        return toAjax(activitySignDetailService.save(activitySignDetail));
    }

    /**
     * 修改签到明细
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:edit')")
    @Log(title = "签到明细管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ActivitySignDetail activitySignDetail) {
        return toAjax(activitySignDetailService.updateById(activitySignDetail));
    }

    /**
     * 删除签到明细
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:remove')")
    @Log(title = "签到明细管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(activitySignDetailService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 根据签到ID分页查询签到明细列表（支持姓名模糊搜索）
     */
    @ApiOperation(value = "根据签到ID分页查询签到明细列表")
    @PreAuthorize("@ss.hasPermi('activity:signdetail:list')")
    @PostMapping("/pageBySignId")
    public TableDataInfo pageBySignId(@RequestBody ActivitySignDetailPageDTO dto) {
        IPage<ActivitySignDetailVO> page = activitySignDetailService.getSignDetailPage(dto);
        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 批量签到
     */
    @ApiOperation(value = "批量签到")
    @PreAuthorize("@ss.hasPermi('activity:signdetail:edit')")
    @Log(title = "批量签到", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSignIn")
    public AjaxResult batchSignIn(@RequestBody ActivitySignDetailBatchDTO dto) {
        int successCount = activitySignDetailService.batchSignIn(dto);
        return success("成功签到 " + successCount + " 人");
    }

    /**
     * 请假
     */
    @ApiOperation(value = "请假")
    @PreAuthorize("@ss.hasPermi('activity:signdetail:edit')")
    @Log(title = "请假", businessType = BusinessType.UPDATE)
    @PostMapping("/leave")
    public AjaxResult leave(@RequestBody ActivitySignDetailLeaveDTO dto) {
        return toAjax(activitySignDetailService.leaveRequest(dto));
    }

    /**
     * 获取当前用户指定活动的签到详情
     */
    @ApiOperation(value = "获取当前用户指定活动的签到详情")
    @PostMapping("/getCurrentUserActivitySignDetails")
    public TableDataInfo getCurrentUserActivitySignDetails(@RequestBody UserActivitySignDetailDTO dto) {
        IPage<UserActivitySignDetailVO> page = activitySignDetailService.getCurrentUserActivitySignDetails(dto);
        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 当前登录用户指定参加/不参加/请假
     */
    @ApiOperation(value = "当前登录用户指定参加/不参加/请假")
    @PostMapping("/updateUserActivitySignDetails")
    public AjaxResult updateUserActivitySignDetails(@RequestBody UpdateActivitySignDetailDTO dto) {
        return toAjax(activitySignDetailService.updateUserActivitySignDetails(dto));
    }

    /**
     * 当前登录用户签到/请假
     */
    @ApiOperation(value = "当前登录用户签到/请假")
    @PostMapping("/userUpdateSignStatus")
    public AjaxResult signOrLeave(@RequestBody @Validated UserUpdateActivitySignDetailStatusDTO dto) {
        return toAjax(activitySignDetailService.updateUserActivitySignDetailsStatus(dto));
    }

    /**
     * 取消签到状态（包括签到和请假状态）
     */
    @PreAuthorize("@ss.hasPermi('activity:signdetail:reset')")
    @ApiOperation(value = "取消签到状态（包括签到和请假状态）")
    @GetMapping("/resetSignStatus/{id}")
    public AjaxResult cancelSignStatus(@PathVariable("id") String signDetailId) {
        return toAjax(activitySignDetailService.resetSignStatus(signDetailId));
    }
}