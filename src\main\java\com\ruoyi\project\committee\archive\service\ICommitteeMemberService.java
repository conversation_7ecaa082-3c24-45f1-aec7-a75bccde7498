package com.ruoyi.project.committee.archive.service;

import com.ruoyi.project.committee.archive.domain.dto.CommitteeMemberQueryDTO;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberQueryVo;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberVo;

import java.util.List;

public interface ICommitteeMemberService {

    CommitteeMemberVo getMemberInfo(String userId);

    /**
     * 根据条件查询委员信息
     *
     * @param queryDTO 查询条件DTO
     * @return 委员信息列表
     */
    List<CommitteeMemberQueryVo> selectCommitteeMemberByCondition(CommitteeMemberQueryDTO queryDTO);
}
