package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 当前用户活动签到明细查询DTO
 */
@Data
@ApiModel(value = "当前用户活动签到明细查询DTO")
public class UserActivitySignDetailDTO {

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityPkid;

    /**
     * 签到说明
     */
    @ApiModelProperty(value = "签到说明", required = false)
    private String signDesc;
    

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", required = true)
    private Integer currentPage = 1;
    
    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true)
    private Integer pageSize = 10;
} 