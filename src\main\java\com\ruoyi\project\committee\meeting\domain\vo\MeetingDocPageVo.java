package com.ruoyi.project.committee.meeting.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MeetingDocPageVo {

    private String id;

    @ApiModelProperty(value = "会议名称")
    private String title;

    @ApiModelProperty(value = "会议类型")
    private String docType;

    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "是否公开")
    private Boolean isPublicly;
}
