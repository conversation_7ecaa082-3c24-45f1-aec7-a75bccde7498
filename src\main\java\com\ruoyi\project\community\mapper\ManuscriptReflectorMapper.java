package com.ruoyi.project.community.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.community.domain.ManuscriptReflector;
import com.ruoyi.project.community.domain.vo.ManuscriptReflectorVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ManuscriptReflectorMapper extends BaseMapper<ManuscriptReflector> {

    default void deleteManuscriptReflector(String id) {
        this.delete(new LambdaQueryWrapper<ManuscriptReflector>()
                .eq(ManuscriptReflector::getManuscriptId, id));
    }


    List<ManuscriptReflectorVo> getManuscriptReflectorList(String manuscriptId);

    default List<ManuscriptReflector> getUserManuscript(String userId) {
        return selectList(new LambdaQueryWrapper<ManuscriptReflector>()
                .eq(ManuscriptReflector::getUserId, userId)
        );
    }
}
