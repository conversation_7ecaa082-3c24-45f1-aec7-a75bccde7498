package com.ruoyi.project.proposal.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.proposal.domain.ProposalAnnexRel;
import com.ruoyi.project.proposal.mapper.ProposalAnnexRelMapper;
import com.ruoyi.project.proposal.service.IProposalAnnexRelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class ProposalAnnexRelServiceImpl extends ServiceImpl<ProposalAnnexRelMapper, ProposalAnnexRel> implements IProposalAnnexRelService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertProposalAnnexRel(Long proposalId, List<Long> annexIdList) {
        List<ProposalAnnexRel> proposalAnnexRelList = new ArrayList<>();

        for (Long annexId : annexIdList) {
            ProposalAnnexRel proposalAnnexRel = new ProposalAnnexRel();
            proposalAnnexRel.setProposalId(String.valueOf(proposalId));
            proposalAnnexRel.setAnnexId(annexId);

            proposalAnnexRelList.add(proposalAnnexRel);
        }

        saveBatch(proposalAnnexRelList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProposalAnnexRel(Long proposalId, List<Long> annexIdList) {
        // 删除关联关系
        deleteProposalAnnexRel(proposalId);
        // 添加关联关系
        if (ObjectUtil.isNotEmpty(annexIdList)) {
            insertProposalAnnexRel(proposalId, annexIdList);
        }
    }

    @Override
    public void deleteProposalAnnexRel(Long proposalId) {
        remove(new LambdaQueryWrapper<ProposalAnnexRel>()
                .eq(ProposalAnnexRel::getProposalId, proposalId));
    }
}
