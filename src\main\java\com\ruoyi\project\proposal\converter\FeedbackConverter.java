package com.ruoyi.project.proposal.converter;

import com.ruoyi.project.proposal.domain.ProposalFeedback;
import com.ruoyi.project.proposal.domain.dto.ProposalFeedbackDto;
import com.ruoyi.project.proposal.domain.vo.ProposalFeedbackUnitVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FeedbackConverter {

    FeedbackConverter INSTANCE = Mappers.getMapper(FeedbackConverter.class);

    ProposalFeedback convert(ProposalFeedbackDto dto);

    ProposalFeedbackUnitVo convert(ProposalFeedback feedback);
}
