package com.ruoyi.project.committee.meeting.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.enums.committee.SignTypeEnum;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 会议签到详情对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meeting_sign_detail")
public class MeetingSignDetail extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 会议id
     */
    private Long meetingId;

    /**
     * 签到信息id
     */
    private Long signId;

    /**
     * 签到委员id
     */
    private Long peopleId;

    /**
     * 开始时间
     */
    private Date beginDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 是否签到
     */
    private Boolean isSign;

    /**
     * 签到类型
     */
    private SignTypeEnum signType;

    /**
     * 是否请假
     */
    private Boolean isLeave;

    /**
     * 请假理由
     */
    private String reason;

    /**
     * 是否领导
     */
    private String isLeader;


    /**
     * 逻辑删除标志位
     */
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private String remark;
}
