package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.common.enums.proposal.CaseTypeEnum;
import com.ruoyi.common.enums.proposal.InstructionEnum;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;
import com.ruoyi.project.proposal.domain.ProposalUserRel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

@Data
public class ProposalEditVo {

    private String id;

    private Long year;

    private Integer caseNumber;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerDate;

    private List<ProposerVo> casePerson;

    private SubmitTypeEnum submitType;

    private List<InstructionEnum> instructions;

    private Boolean isOpen;

    private String caseReason;

    private String caseContent;

    private CaseFillingEnum caseFiling;

    private String remark;

    private List<Long> annexList;

    private String[] ids;
}
