<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalUserRelMapper">

    <select id="getProposalUserById" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUserRelVo">
        SELECT
            u.user_id AS id,
            u.user_name AS userName,
            pur.submit_type AS submitType,
            u.phonenumber AS telephone,
            d.phone AS companyPhone,
            d.address AS companyAddress,
            d.postal_code AS companyPostalCode,
            p.post_name AS position
        FROM proposal_user_rel pur
            LEFT JOIN sys_user u ON u.user_id = pur.proposer_id
            LEFT JOIN sys_dept d ON d.dept_id = u.dept_id
            LEFT JOIN sys_user_post up ON up.user_id = u.user_id
            LEFT JOIN sys_post p ON p.post_id = up.post_id
        WHERE pur.proposal_id = #{id}
            AND pur.del_flag = 0
    </select>

    <update id="deleteProposalUserRelByIds" parameterType="String">
        update proposal_user_rel set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>