package com.ruoyi.project.committee.archive.service.impl;

import com.ruoyi.common.enums.committee.ElectedPeriodEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.committee.archive.converter.CommitteeMemberConverter;
import com.ruoyi.project.committee.archive.domain.dto.CommitteeMemberQueryDTO;
import com.ruoyi.project.committee.archive.service.ICommitteeMemberService;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberQueryVo;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberVo;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.archive.mapper.SectorMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CommitteeMemberServiceImpl implements ICommitteeMemberService {

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    @Resource
    private SectorMapper sectorMapper;

    @Override
    public CommitteeMemberVo getMemberInfo(String userId) {
        CommitteeMember memberInfo = committeeMemberMapper.getMemberInfo(userId);
        return CommitteeMemberConverter.INSTANCE.convertTo(memberInfo);
    }

    @Override
    public List<CommitteeMemberQueryVo> selectCommitteeMemberByCondition(CommitteeMemberQueryDTO queryDTO) {

        // 如果electedTimes不为空，则优先使用electedTimes，将electedPeriod设置为null
        if (StringUtils.isNotEmpty(queryDTO.getElectedTimes())) {
            queryDTO.setElectedPeriod(null);
            System.out.println("Using electedTimes: " + queryDTO.getElectedTimes());
        } else if (StringUtils.isNotEmpty(queryDTO.getElectedPeriod())) {
            System.out.println("Using electedPeriod: " + queryDTO.getElectedPeriod());
        }

        // 执行查询
        List<CommitteeMemberQueryVo> results = committeeMemberMapper.selectCommitteeMemberByCondition(
            queryDTO.getElectedPeriod(),
            queryDTO.getElectedTimes(),
            queryDTO.getUserName(),
            queryDTO.getSector(),
            queryDTO.getBelongsSpecialCommittee(),
            queryDTO.getYear()
        );

        if (results != null && !results.isEmpty()) {
            System.out.println("First result: " + results.get(0).getUserName());
        }

        return results;
    }
}
