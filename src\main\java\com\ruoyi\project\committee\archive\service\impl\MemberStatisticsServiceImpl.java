package com.ruoyi.project.committee.archive.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.project.committee.archive.converter.MemberStatisticsConverter;
import com.ruoyi.project.committee.archive.service.IMemberStatisticsService;
import com.ruoyi.project.committee.archive.domain.MemberStatistics;
import com.ruoyi.project.committee.archive.domain.dto.MemberStatisticsEditVo;
import com.ruoyi.project.committee.archive.domain.dto.MemberStatisticsPageDto;
import com.ruoyi.project.committee.archive.domain.vo.MemberCountVo;
import com.ruoyi.project.committee.archive.domain.vo.MemberStatisticsVo;
import com.ruoyi.project.committee.archive.mapper.MemberStatisticsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MemberStatisticsServiceImpl implements IMemberStatisticsService {

    @Resource
    private MemberStatisticsMapper memberStatisticsMapper;


    @Override
    public IPage<MemberStatisticsVo> selectMemberStatisticsPage(MemberStatisticsPageDto pageDto) {
        Page<MemberStatisticsVo> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        return memberStatisticsMapper.selectMemberStatisticsPage(page, pageDto);
    }

    @Override
    public Long add(MemberStatisticsEditVo editVo) {
        editVo.setId(null);
        MemberStatistics memberStatistics = MemberStatisticsConverter.INSTANCE.convertTo(editVo);
        memberStatistics.setYear(DateUtil.date().year());
        if (memberStatisticsMapper.existInfo(memberStatistics)) {
            throw new ServiceException("该记录已存在");
        }

        memberStatisticsMapper.insert(memberStatistics);
        return memberStatistics.getId();
    }

    @Override
    public Boolean edit(MemberStatisticsEditVo editVo) {
        MemberStatistics memberStatistics = MemberStatisticsConverter.INSTANCE.convertTo(editVo);
        memberStatisticsMapper.updateById(memberStatistics);
        return true;
    }

    @Override
    public Boolean delete(String id) {
        memberStatisticsMapper.deleteById(id);
        return true;
    }

    @Override
    public MemberCountVo getById(String id) {

        MemberStatistics memberStatistics = memberStatisticsMapper.selectById(id);
        MemberCountVo memberCountVo = new MemberCountVo();
        memberCountVo.setOpinionsNum(memberStatistics.getOpinions());
        memberCountVo.setProposalNum(memberStatistics.getProposals());

        return memberCountVo;
    }
}
