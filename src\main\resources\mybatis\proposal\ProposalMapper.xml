<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.ProposalMapper">

    <resultMap id="proposalPageVo" type="com.ruoyi.project.proposal.domain.vo.ProposalPageVo">
        <id property="id" column="id"/>
        <result property="year" column="year"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="caseNumber" column="case_number"/>
        <result property="caseReason" column="case_reason"/>
        <result property="registerDate" column="register_date"/>
        <result property="caseFiling" column="case_filing"/>
        <result property="proposer" column="proposer"/>
    </resultMap>


    <resultMap id="proposalComprehensivePageVo" type="com.ruoyi.project.proposal.domain.vo.ProposalComprehensivePageVo">
        <id property="id" column="id"/>
        <result property="year" column="year"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="caseNumber" column="case_number"/>
        <result property="caseType"   column="case_type"/>
        <result property="caseReason" column="case_reason"/>
        <result property="registerDate" column="register_date"/>
        <result property="feedbackDate" column="feedback_time"/>
        <result property="organizers" column="organizers"/>
        <result property="undertakeWay" column="undertake_way" />
        <result property="caseFiling" column="case_filing"/>
        <result property="proposer" column="proposer"/>
        <result property="handleId" column="handle_id"/>
<!--        <collection property="organizerList"-->
<!--                    ofType="java.lang.String"-->
<!--                    column="handle_id"-->
<!--                    select="selectOrganizersById"/>-->
    </resultMap>


    <select id="selectProposalPage" resultMap="proposalPageVo">
        SELECT
            p.id,
            p.year,
            p.serial_number,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason,
            p.case_filing,
            p.register_date,
            p.proposer
        FROM proposal p
        WHERE p.del_flag = #{pageParam.isDeleted}
            <if test="pageParam.year != null and pageParam.year.length == 2">
                AND p.year BETWEEN #{pageParam.year[0]} AND #{pageParam.year[1]}
            </if>
            <if test="pageParam.caseType != null">
                AND p.case_type = #{pageParam.caseType}
            </if>
            <if test="pageParam.caseReason != null and pageParam.caseReason != ''">
                AND p.case_reason LIKE concat('%', #{pageParam.caseReason} ,'%')
            </if>
            <if test="pageParam.caseNumber != null and pageParam.caseNumber != ''">
                AND LPAD(p.case_number, 4, '0') LIKE concat('%', #{pageParam.caseNumber} ,'%')
            </if>
            <if test="pageParam.serialNumber != null and pageParam.serialNumber != ''">
                AND p.serial_number LIKE CONCAT('%', #{pageParam.serialNumber}, '%')
            </if>
            <if test="pageParam.proposer != null and pageParam.proposer != ''">
                AND p.proposer LIKE  concat('%', #{pageParam.proposer} ,'%')
            </if>
            <if test="pageParam.caseFiling != null" >
                AND p.case_filing = #{pageParam.caseFiling}
            </if>
            <if test="pageParam.isFinalTrial != null">
                AND p.is_final_trial = #{pageParam.isFinalTrial}
            </if>
            <if test="pageParam.caseNumberIsNull != null and pageParam.caseNumberIsNull == true">
                AND p.case_number IS NULL
            </if>
            <if test="pageParam.caseNumberIsNull != null and pageParam.caseNumberIsNull == false">
                AND p.case_number IS NOT NULL
            </if>
            <if test="caseFilingList != null and caseFilingList.size > 0">
                AND EXISTS (
                    SELECT 1 FROM (
                    <foreach item="item" collection="caseFilingList" separator="UNION ALL" index="">
                        SELECT #{item} AS case_filing
                    </foreach>
                    ) t WHERE t.case_filing = p.case_filing
                )
            </if>
        GROUP BY p.id
        ORDER BY p.create_time DESC
    </select>


    <select id="selectProposalAssignPage" resultType="com.ruoyi.project.proposal.domain.vo.ProposalAssignPageVo">
        SELECT
            p.id,
            p.year,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason,
            p.is_assigned,
            p.proposer,
            ph.organizers AS organizer
        FROM proposal p
            LEFT JOIN proposal_handle ph ON ph.proposal_id = p.id
        WHERE p.del_flag = false
            AND p.case_filing IN ('PUT_ON', 'MERGED','WAIT_HANDLE')
            <if test="pageParam.year != null and pageParam.year.length == 2">
                AND p.year BETWEEN #{pageParam.year[0]} AND #{pageParam.year[1]}
            </if>
            <if test="pageParam.caseType != null">
                AND p.case_type = #{pageParam.caseType}
            </if>
            <if test="pageParam.caseReason != null and pageParam.caseReason != ''">
                AND p.case_reason LIKE  concat('%', #{pageParam.caseReason} ,'%')
            </if>
            <if test="pageParam.caseNumber != null and pageParam.caseNumber != ''">
                AND LPAD(p.case_number, 4, '0') LIKE CONCAT('%', #{pageParam.caseNumber}, '%')
            </if>
            <if test="pageParam.proposer != null and pageParam.proposer != ''">
                AND p.proposer LIKE  concat('%', #{pageParam.proposer} ,'%')
            </if>
            <if test="pageParam.isAssigned != null">
                AND p.is_assigned = #{pageParam.isAssigned}
            </if>
            <if test="pageParam.isReceived != null">
                AND p.is_received = #{pageParam.isFinalTrial}
            </if>
        ORDER BY p.id DESC
    </select>


    <select id="selectComprehensiveProposalPage" resultMap="proposalComprehensivePageVo">
        SELECT DISTINCT
            p.id,
            p.year,
            p.serial_number,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_type,
            p.case_reason,
            p.register_date,
            (SELECT MAX(create_time) FROM proposal_feedback pf WHERE pf.proposal_id = p.id) AS feedback_time,
            ph.undertake_way,
            p.case_filing,
            p.proposer,
            p.create_time,
            ph.organizers,
            ph.id AS handle_id
        FROM proposal p
            LEFT JOIN proposal_handle ph ON ph.proposal_id = p.id
        WHERE p.del_flag = false
            <if test="pageParam.year != null and pageParam.year.length == 2">
                AND p.year BETWEEN #{pageParam.year[0]} AND #{pageParam.year[1]}
            </if>
            <if test="pageParam.serialNumber != null and pageParam.serialNumber != ''">
                AND p.serial_number LIKE CONCAT('%', TRIM(LEADING '0' FROM #{pageParam.serialNumber}), '%')
            </if>
            <if test="pageParam.caseNumber != null and pageParam.caseNumber != ''">
                AND LPAD(p.case_number, 4, '0') LIKE CONCAT('%', TRIM(LEADING '0' FROM #{pageParam.caseNumber}), '%')
            </if>
            <if test="pageParam.caseType != null">
                AND p.case_type= #{pageParam.caseType}
            </if>
            <if test="pageParam.submitType != null">
                AND p.submit_type = #{pageParam.submitType}
            </if>
            <if test="pageParam.caseFiling != null">
                AND p.case_filing = #{pageParam.caseFiling}
            </if>
            <if test="pageParam.caseReason != null and pageParam.caseReason != ''">
                AND p.case_reason LIKE  concat('%', #{pageParam.caseReason} ,'%')
            </if>
            <if test="pageParam.proposer != null and pageParam.proposer != ''">
                AND p.proposer LIKE  concat('%', #{pageParam.proposer} ,'%')
            </if>
            <if test="pageParam.undertakeWay != null and pageParam.undertakeWay != ''">
                AND ph.undertake_way = #{pageParam.undertakeWay}
            </if>
            <if test="pageParam.isAssigned != null">
                AND p.is_assigned = #{pageParam.isAssigned}
            </if>
            <if test="ids != null and ids.size() > 0">
                AND p.id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        ORDER BY p.create_time DESC
    </select>


    <select id="selectUsersByProposalId" resultType="com.ruoyi.project.proposal.domain.vo.ProposalUserVo">
        SELECT
            u.user_id,
            u.user_name,
            pur.submit_type
        FROM proposal_user_rel pur
            JOIN sys_user u ON pur.user_id = u.user_id
        WHERE pur.proposal_id = #{id} AND pur.del_flag = false
    </select>


    <select id="selectProposalWordVoById" resultType="com.ruoyi.project.proposal.domain.vo.ProposalWordVo">
        SELECT
            LPAD(p.serial_number, 4, '0') AS serialNumber,
            DATE_FORMAT(p.register_date, '%Y.%m.%d') AS registerDate,
            p.case_reason AS caseReason,
            p.case_content AS caseContent,
            p.proposer
        FROM proposal p
        WHERE p.id = #{id}
            AND p.del_flag = false
    </select>

    <select id="batchUpdateProposalAssignStatus" resultType="Integer">
        UPDATE proposal
        SET is_assigned = true
        WHERE del_flag = false
          AND is_assigned = false
          AND
            id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectComprehensiveInfoById" resultType="com.ruoyi.project.proposal.domain.vo.ProposalComprehensiveVo">
        SELECT
            p.id,
            p.year,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason,
            p.case_content,
            p.case_type,
            p.case_category,
            p.proposer,
            p.remark,
            p.instructions,
            p.submit_type,
            p.is_open,
            p.is_assigned,
            ph.organizers,
            ph.undertake_way,
            ph.undertake_result,
            ph.undertake_time,
            p.case_filing,
            p.track
        FROM proposal p
            LEFT JOIN proposal_handle ph ON ph.proposal_id = p.id
        WHERE p.del_flag = false
          AND p.id = #{id}
    </select>

    <update id="batchDeleteProposal" parameterType="long[]">
        UPDATE proposal
        SET del_flag = true
        WHERE id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </update>

    <update id="mergeProposal">
        UPDATE proposal
        <set>
            case_number = #{caseNumber},
            case_filing = 'MERGED',
            case_reason = CONCAT(case_reason, #{caseReason}),
            case_filing = #{caseFiling}
        </set>
        WHERE id = #{proposalId}
    </update>


    <select id="getProposerMap" resultType="java.util.HashMap">
        SELECT
            p.id,
            u.user_name
        FROM proposal p
            JOIN proposal_user_rel pur ON pur.proposal_id = p.id AND pur.del_flag = false
            JOIN sys_user u ON u.user_id = pur.proposer_id
        WHERE p.del_flag = false
          AND p.id IN
        <foreach collection="proposalIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getSupervisionPage" resultType="com.ruoyi.project.proposal.domain.vo.SupervisionPageVo">
        SELECT
            p.id,
            p.year,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_reason,
            p.proposer,
            p.deadline,
            IF(p.overdue = false, '未超期', '已超期') AS overdue,
            p.urge_count
        FROM proposal p
            LEFT JOIN proposal_handle_rel phl ON phl.proposal_id = p.id
            LEFT JOIN proposal_handle ph ON ph.id = phl.handle_id
        WHERE p.del_flag = false
            <if test="pageParam.year != null and pageParam.year.length == 2">
                AND p.year BETWEEN #{pageParam.year[0]} AND #{pageParam.year[1]}
            </if>
            <if test="pageParam.caseType != null and pageParam.caseType != ''">
                AND p.case_type = #{pageParam.caseType}
            </if>
            <if test="pageParam.caseNumber != null and pageParam.caseNumber != ''">
                AND p.case_number LIKE CONCAT('%', #{pageParam.caseNumber}, '%')
            </if>
            <if test="pageParam.caseReason != null and pageParam.caseReason != ''">
                AND p.case_reason LIKE concat('%', #{pageParam.caseReason}, '%')
            </if>
            <if test="pageParam.proposer != null and pageParam.proposer != ''">
                AND p.proposer LIKE concat('%', #{pageParam.proposer}, '%')
            </if>
            <if test="pageParam.isFinalTrial != null">
                AND p.is_finished = #{pageParam.isFinished}
            </if>
            <if test="pageParam.overdue != null">
                AND p.overdue = #{pageParam.overdue}
            </if>
    </select>

    <select id="getProposalCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM proposal
        WHERE del_flag = false
            AND (year BETWEEN #{year} AND #{year})
    </select>

    <select id="selectOrganizersById" resultType="java.lang.String">
        SELECT
            d.dept_name
        FROM proposal_handle_organizer pho
                 JOIN sys_dept d ON pho.dept_id = d.dept_id
        WHERE pho.handle_id = #{handleId}
    </select>

    <select id="getUserProposal" resultType="proposalComprehensivePageVo">
        SELECT DISTINCT
            p.id,
            p.year,
            p.serial_number,
            LPAD(p.case_number, 4, '0') AS case_number,
            p.case_type,
            p.case_reason,
            p.register_date,
            pf.create_time AS feedback_time,
            ph.undertake_way,
            p.case_filing,
            p.proposer,
            p.create_by,
            p.create_time,
            ph.id AS handle_id
        FROM proposal p
            LEFT JOIN proposal_handle_rel phl ON phl.proposal_id = p.id
            LEFT JOIN proposal_handle ph ON ph.id = phl.handle_id
            LEFT JOIN proposal_feedback pf ON pf.proposal_id = p.id
        WHERE p.del_flag = false
          AND
            ( p.create_by = #{pageParam.username}
            OR
                (SELECT COUNT(*)
                FROM proposal_user_rel pur
                WHERE pur.proposal_id = p.id
                AND pur.proposer_id = #{proposerRelId}) > 0
            )

        ORDER BY p.create_time DESC
    </select>

    <select id="selectSerialNumber" resultType="java.lang.Integer">
        SELECT
            IFNULL(MAX(serial_number) + 1, 1) AS serial_number
        FROM proposal
        WHERE del_flag = false
            AND `year` = YEAR(curdate())
    </select>

    <select id="selectCaseNumber" resultType="java.lang.Integer">
        SELECT
            IFNULL(MAX(case_number) + 1, 1) AS case_number
        FROM proposal
        WHERE del_flag = false
          AND `year` = YEAR(curdate())
    </select>

    <select id="getMergeInfo" resultType="com.ruoyi.project.proposal.domain.ProposalMerge">
        SELECT *
        FROM proposal_merge
        WHERE main_proposal = #{proposalId}
    </select>
</mapper>