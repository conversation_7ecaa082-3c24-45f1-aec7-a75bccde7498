package com.ruoyi.project.activity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 活动签到明细实体类
 */
@Data
//@EqualsAndHashCode(callSuper = true)
@TableName("activity_sign_detail")
public class ActivitySignDetail{
    
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 原UUID主键
     */
    @TableField("PKID")
    private String pkid;
    
    /**
     * 签到记录ID
     */
    @TableField("sign_pkid")
    private String signPkid;
    
    /**
     * 人员ID
     */
    @TableField("Pepole_PKID")
    private String pepolePkid;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Begin_Date")
    private Date beginDate;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("End_Date")
    private Date endDate;
    
    /**
     * 是否签到(1:是 0:否)
     */
    @TableField("Is_Sign")
    private String isSign;
    
    /**
     * 签到类型
     */
    @TableField("Sign_Type")
    private String signType;
    
    /**
     * 是否请假(1:是 0:否)
     */
    @TableField("Is_Leave")
    private String isLeave;
    
    /**
     * 原因说明
     */
    @TableField("Reason")
    private String reason;
    
    /**
     * 审核人ID
     */
    @TableField("audit_id")
    private String auditId;
    
    /**
     * 审核状态
     */
    @TableField("audit_state")
    private String auditState;
    
    /**
     * 审核时间
     */
    @TableField("audit_time")
    private String auditTime;
    
    /**
     * 审核意见
     */
    @TableField("audit_opinion")
    private String auditOpinion;
    
    /**
     * 审核人姓名
     */
    @TableField("audit_name")
    private String auditName;
    
    /**
     * 活动ID
     */
    @TableField("activity_pkid")
    private String activityPkid;
    
    /**
     * 是否负责人(1:是 0:否)
     */
    @TableField("is_leader")
    private String isLeader;
    
    /**
     * 区域ID
     */
    @TableField("region_id")
    private String regionId;
} 