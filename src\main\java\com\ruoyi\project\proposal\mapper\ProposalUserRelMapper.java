package com.ruoyi.project.proposal.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalUserRel;
import com.ruoyi.project.proposal.domain.vo.ProposalUserRelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 提案人员关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Mapper
public interface ProposalUserRelMapper extends BaseMapper<ProposalUserRel>{

    public List<ProposalUserRelVo> getProposalUserById(@Param("id") String id);

    /**
     * 批量删除提案人员关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProposalUserRelByIds(String[] ids);

    /**
     * 根据提案者id查询提案人员关联
     * @param proposerId proposerId
     * @return result
     */
    default List<ProposalUserRel> selectByProposerId(String proposerId) {
        return selectList(new LambdaQueryWrapper<ProposalUserRel>()
                .eq(ProposalUserRel::getProposerId, proposerId));
    }
}
