package com.ruoyi.project.committee.resumption.converter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.committee.resumption.domain.dto.PriorityEditDto;
import com.ruoyi.project.committee.resumption.domain.po.Priority;
import com.ruoyi.project.committee.resumption.domain.vo.PriorityPageVo;
import com.ruoyi.project.committee.resumption.domain.vo.PriorityVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 重点工作信息转换器
 */
@Mapper
public interface PriorityConverter {

    PriorityConverter INSTANCE = Mappers.getMapper(PriorityConverter.class);

    /**
     * DTO转Entity
     */
    Priority convert(PriorityEditDto dto);

    /**
     * Entity转VO
     */
    PriorityVo entityToVo(Priority entity);

    /**
     * Entity列表转VO列表
     */
    List<PriorityVo> entityListToVoList(List<Priority> entityList);

    Page<PriorityPageVo> convertToPage(IPage<Priority> page);
}
