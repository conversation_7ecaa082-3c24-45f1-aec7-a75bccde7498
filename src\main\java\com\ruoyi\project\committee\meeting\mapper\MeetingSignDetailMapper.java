package com.ruoyi.project.committee.meeting.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.enums.committee.SignTypeEnum;
import com.ruoyi.project.committee.meeting.domain.MeetingSignDetail;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignOperateDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingSignDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 会议签到详情 Mapper 接口
 */
@Mapper
public interface MeetingSignDetailMapper extends BaseMapper<MeetingSignDetail> {

    /**
     * 获取会议签到详情分页列表
     *
     * @param signId 查询参数
     * @return 分页结果
     */
    List<MeetingSignDetailVo> selectMeetingSignDetailList(@Param("signId") String signId);

    MeetingSignDetail getParticipantSignDetail(Long signId, Long currentUserId);

    /**
     * 批量签到
     * @param operationDto operationDto
     * @return affect rows
     */
    default Integer batchSignIn(MeetingSignOperateDto operationDto) {
        return update(null, new LambdaUpdateWrapper<MeetingSignDetail>()
                .set(MeetingSignDetail::getIsSign, true)
                .set(MeetingSignDetail::getBeginDate, new Date())
                .set(MeetingSignDetail::getSignType, SignTypeEnum.PROXY)
                .in(MeetingSignDetail::getId, operationDto.getSignDetailIdList())
        );
    }

    default Integer leave(MeetingSignOperateDto operationDto) {
        return update(null, new LambdaUpdateWrapper<MeetingSignDetail>()
                .set(MeetingSignDetail::getIsLeave, true)
                .set(MeetingSignDetail::getIsSign, false)
                .set(MeetingSignDetail::getReason, operationDto.getReason())
                .set(MeetingSignDetail::getSignType, SignTypeEnum.UNSIGNED)
                .set(MeetingSignDetail::getBeginDate, new Date())
                .eq(MeetingSignDetail::getId, operationDto.getSignDetailIdList().get(0))
        );

    }

    default Integer cancel(MeetingSignOperateDto operationDto) {
        return update(null, new LambdaUpdateWrapper<MeetingSignDetail>()
                .set(MeetingSignDetail::getIsLeave, false)
                .set(MeetingSignDetail::getIsSign, false)
                .set(MeetingSignDetail::getReason, null)
                .set(MeetingSignDetail::getSignType, SignTypeEnum.UNSIGNED)
                .set(MeetingSignDetail::getBeginDate, new Date())
                .eq(MeetingSignDetail::getId, operationDto.getSignDetailIdList().get(0))
        );
    };
}
