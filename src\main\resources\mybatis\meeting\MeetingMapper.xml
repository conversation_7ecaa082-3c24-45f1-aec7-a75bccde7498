<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.meeting.mapper.MeetingMapper">

    <select id="getMyMeetingPage" resultType="com.ruoyi.project.committee.meeting.domain.vo.MeetingInfoPageVo">
        SELECT
            m.id,
            mp.id AS participant_id,
            m.meeting_name,
            m.meeting_type,
            m.is_publish,
            m.start_time,
            m.end_time,
            m.meeting_address,
            mp.is_attend
        FROM meeting_info m
            LEFT JOIN meeting_participants mp ON mp.meeting_id = m.id AND mp.del_flag = false
            LEFT JOIN sys_committee_member cm ON cm.id = mp.people_id
        WHERE m.del_flag = false
            AND m.is_publish = true
            AND cm.user_id = #{userId}
        <if test="pageDto.meetingName != null and pageDto.meetingName != ''">
            AND m.meeting_name LIKE CONCAT('%', #{pageDto.meetingName}, '%')
        </if>
        <if test="pageDto.meetingType != null">
            AND m.meeting_type = #{pageDto.meetingType}
        </if>
        <if test="pageDto.startTime != null">
            AND m.start_time &gt;= #{pageDto.startTime}
        </if>
        <if test="pageDto.endTime != null">
            AND m.end_time &lt;= #{pageDto.endTime}
        </if>
        ORDER BY m.create_time DESC
    </select>
</mapper>