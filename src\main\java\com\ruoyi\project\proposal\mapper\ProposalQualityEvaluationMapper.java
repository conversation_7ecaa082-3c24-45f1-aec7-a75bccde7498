package com.ruoyi.project.proposal.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalQualityEvaluation;
import com.ruoyi.project.proposal.domain.vo.ProposalQualityEvaluationVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 质量评价信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Mapper
public interface ProposalQualityEvaluationMapper extends BaseMapper<ProposalQualityEvaluation> {

    /**
     * 查询质量评价信息列表
     * 
     * @param proposalQualityEvaluation 质量评价信息
     * @return 质量评价信息集合
     */
    public List<ProposalQualityEvaluation> selectProposalQualityEvaluationList(ProposalQualityEvaluation proposalQualityEvaluation);

    /**
     * 批量删除质量评价信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProposalQualityEvaluationByIds(Long[] ids);

    /**
     * 查询质量评价信息列表
     *
     * @param proposalId 提案id
     * @return 质量评价信息集合
     */
    List<ProposalQualityEvaluationVo> selectEvaluationList(Long proposalId);
}
