package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新活动通知DTO
 */
@Data
@ApiModel(value = "更新活动通知DTO")
public class ActivityNotificationUpdateDTO {

    /**
     * 通知ID
     */
    @NotNull(message = "通知ID不能为空")
    @NotBlank(message = "通知ID不能为空")
    @ApiModelProperty(value = "通知ID", required = true)
    private String id;

    /**
     * 通知标题
     */
    @ApiModelProperty(value = "通知标题")
    private String title;

    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
    private String content;

    /**
     * 是否发送短信
     */
    @ApiModelProperty(value = "是否发送短信")
    private Boolean isSendSms;
}