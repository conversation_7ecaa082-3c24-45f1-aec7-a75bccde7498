package com.ruoyi.project.proposal.domain.vo;

import com.ruoyi.common.enums.proposal.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProposalHandlePageParamVo {

    private Long currentPage = 1L;

    private Long pageSize = 10L;

    @ApiModelProperty(value = "提案年度")
    private Integer[] year;

    @ApiModelProperty(value = "统计年度")
    private Integer statisticalYear;

    @ApiModelProperty(value = "提案案号")
    private Integer caseNumber;

    @ApiModelProperty(value = "提案案由")
    private String caseReason;

    @ApiModelProperty(value = "提案类别")
    private CaseTypeEnum caseType;

    @ApiModelProperty(value = "提案者(关键字)")
    private String proposer;

    @ApiModelProperty(value = "承办单位")
    private String organizers;

    @ApiModelProperty(value = "是否办结")
    private Boolean isHandle;

    @ApiModelProperty(value = "是否评价")
    private Boolean evaluationStatus;

    @ApiModelProperty(value = "承办人员")
    private String undertakePerson;

    @ApiModelProperty(value = "承办方式")
    private UndertakeWayEnum undertakeWay;

    @ApiModelProperty(value = "办结时间")
    private Date undertakeTime;

    @ApiModelProperty(value = "反馈状态")
    private Boolean feedbackStatus;

    @ApiModelProperty(value = "办理方式")
    private HandleWayEnum handleWay;

    @ApiModelProperty(value = "提案质量")
    private QualityEnum proposalQuality;

    @ApiModelProperty(value = "办理结果")
    private UndertakeResultEnum undertakeResult;

    @ApiModelProperty(value = "性质")
    private String nature;
}
