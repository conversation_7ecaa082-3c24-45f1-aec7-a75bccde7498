
--
-- 活动管理 活动通知表
--
DROP TABLE IF EXISTS `activity_notification`;

CREATE TABLE `activity_notification`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `activity_id` bigint NOT NULL COMMENT '活动id',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知内容',
  `is_send_sms` tinyint(1) NULL DEFAULT 0 COMMENT '是否发送短信',
  `is_published` tinyint(1) NULL DEFAULT 0 COMMENT '通知是否已发布',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '上次更新操作更新人id',
  `update_at` datetime NULL DEFAULT NULL COMMENT '上次更新时间',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标志位',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_activity_id`(`activity_id` ASC) USING BTREE,
  CONSTRAINT `rk_activity_id` FOREIGN KEY (`activity_id`) REFERENCES `activity_basicinfo` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;


--
-- 活动管理 活动通知 通知接收情况表
--
DROP TABLE IF EXISTS `activity_notification_reception`;

CREATE TABLE `activity_notification_reception`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `notification_id` bigint NOT NULL COMMENT '通知id',
  `recipient_id` bigint NOT NULL COMMENT '接收人id',
  `receive_time` datetime NULL DEFAULT NULL COMMENT '接收时间',
  `receive_status` tinyint(1) NULL DEFAULT 0 COMMENT '接收状态',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `create_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '上次更新操作更新人id',
  `update_at` datetime NULL DEFAULT NULL COMMENT '上次更新时间',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标识',
  PRIMARY KEY (`id` DESC) USING BTREE,
  UNIQUE INDEX `uk_nr_id`(`notification_id` ASC, `recipient_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1934905862162255887 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;