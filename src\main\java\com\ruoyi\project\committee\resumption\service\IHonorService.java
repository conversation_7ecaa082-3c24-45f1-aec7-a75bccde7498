package com.ruoyi.project.committee.resumption.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.domain.dto.PageDTO;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.committee.resumption.domain.dto.HonorAddDTO;
import com.ruoyi.project.committee.resumption.domain.dto.HonorAuditDTO;
import com.ruoyi.project.committee.resumption.domain.dto.HonorUpdateDTO;
import com.ruoyi.project.committee.resumption.domain.po.Honor;
import com.ruoyi.project.committee.resumption.domain.query.HonorPageQuery;
import com.ruoyi.project.committee.resumption.domain.query.HonorUserPageQuery;
import com.ruoyi.project.committee.resumption.domain.vo.HonorDetailVo;
import com.ruoyi.project.committee.resumption.domain.vo.HonorPageVo;

import java.util.List;

/**
 * 获奖情况Service接口
 */
public interface IHonorService extends IService<Honor> {

    /**
     * 获取荣誉列表
     * @param query 查询参数
     * @return 荣誉列表
     */
    PageDTO<HonorPageVo> getHonorList(HonorPageQuery query);

    /**
     * 获取荣誉详情
     * @param id 荣誉ID
     * @return 荣誉详情
     */
    HonorDetailVo getHonorDetail(String id);


    /**
     * 新增荣誉
     * @param honorAddDTO 荣誉信息
     * @return 结果
     */
    AjaxResult addHonor(HonorAddDTO honorAddDTO);

    /**
     * 修改荣誉
     * @param honorUpdateDTO 荣誉信息
     * @return 结果
     */
    AjaxResult updateHonor(HonorUpdateDTO honorUpdateDTO);

    /**
     * 审核荣誉
     * @param honorAuditDTO 审核信息
     * @return 结果
     */
    AjaxResult auditHonor(HonorAuditDTO honorAuditDTO);

    /**
     * 删除荣誉，支持单个或多个ID
     * @param ids 荣誉ID列表
     * @return 结果
     */
    AjaxResult deleteHonor(List<String> ids);

    /**
     * 退回荣誉
     * @param honorAuditDTO 审核信息
     * @return 结果
     */
    AjaxResult returnHonor(HonorAuditDTO honorAuditDTO);

    /**
     * 获取当前用户的荣誉列表
     * @param query 查询参数
     * @return 荣誉列表
     */
    PageDTO<HonorPageVo> getCurrentUserHonorList(HonorUserPageQuery query);
}
