package com.ruoyi.project.committee.meeting.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.enums.committee.MeetingTypeEnum;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会议信息实体类
 * 对应数据库表：meeting_info
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meeting_info")
public class MeetingInfo extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 会议名称
     */
    private String meetingName;

    /**
     * 会议类型
     */
    private MeetingTypeEnum meetingType;

    /**
     * 是否发布
     */
    private Boolean isPublish;

    /**
     * 届
     */
    private String period;

    /**
     * 次
     */
    private String rate;

    /**
     * 会议主题
     */
    private String meetingSubject;

    /**
     * 会议地址
     */
    private String meetingAddress;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;


    /**
     * 逻辑删除标志位 
     */
    @TableLogic
    private Boolean delFlag;

    @TableField(exist = false)
    private String remark;
}
