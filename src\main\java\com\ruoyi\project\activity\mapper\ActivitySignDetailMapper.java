package com.ruoyi.project.activity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.activity.domain.ActivitySignDetail;
import com.ruoyi.project.activity.domain.vo.ActivitySignDetailVO;
import com.ruoyi.project.activity.domain.vo.UserActivitySignDetailVO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 活动签到明细数据层
 */
@Mapper
public interface ActivitySignDetailMapper extends BaseMapper<ActivitySignDetail> {

        /**
         * 根据签到ID和人员姓名分页查询签到明细
         *
         * @param page       分页参数
         * @param signPkid   签到ID
         * @param peopleName 人员姓名（模糊查询）
         * @return 签到明细分页列表
         */
        IPage<ActivitySignDetailVO> getSignDetailPageWithName(Page<ActivitySignDetailVO> page,
                        @Param("signPkid") String signPkid, @Param("peopleName") String peopleName);

        /**
         * 获取当前用户指定活动的签到详情
         *
         * @param page         分页参数
         * @param activityPkid 活动ID
         * @param userId       用户ID
         * @param signDesc     签到说明
         * @return 用户活动签到详情分页列表
         */
        IPage<UserActivitySignDetailVO> getCurrentUserActivitySignDetails(Page<UserActivitySignDetailVO> page,
                        @Param("activityPkid") String activityPkid, @Param("userId") String userId,
                        @Param("signDesc") String signDesc);

        /**
         * 获取当前用户指定活动的签到详情(新实现，之前的有点儿小问题，但是又不想动之前的代码)
         *
         * @param page         分页参数
         * @param activityPkid 活动ID
         * @param userId       用户ID
         * @param signDesc     签到说明
         * @return 用户活动签到详情分页列表
         */
        IPage<UserActivitySignDetailVO> getUserActivitySignDetails(Page<UserActivitySignDetailVO> page,
                        @Param("activityPkid") String activityPkid, @Param("userIds") List<String> userIds,
                        @Param("signDesc") String signDesc);
}