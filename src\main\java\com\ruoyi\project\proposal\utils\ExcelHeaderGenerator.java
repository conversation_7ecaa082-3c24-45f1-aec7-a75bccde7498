package com.ruoyi.project.proposal.utils;



import cn.hutool.poi.excel.ExcelWriter;
import com.ruoyi.common.annotation.Header;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

public class ExcelHeaderGenerator {

    public static List<List<String>> generateHeader(Class<?> clazz) {

        List<List<String>> headerList = new ArrayList<>();
        List<String> firstRow = new ArrayList<>();
        List<String> secondRow = new ArrayList<>();

        // 获取并排序所有带有Header注解的字段
        List<Field> fieldList = Arrays.stream(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(Header.class))
                .sorted(Comparator.comparingInt(field -> field.getAnnotation(Header.class).order()))
                .collect(Collectors.toList());

        // 使用LinkedHashMap保持插入顺序
        Map<String, List<String>> categoryMap = new LinkedHashMap<>();
        for (Field field : fieldList) {
            Header header = field.getAnnotation(Header.class);
            String category = header.category();
            String subCategory = header.subCategory();

            // 如果类别不存在，则创建一个新的列表
            categoryMap.computeIfAbsent(category, k -> new ArrayList<>());

            // 添加子分类到对应的列表中
            List<String> subCategories = categoryMap.get(category);
            boolean found = false;
            for (int i = 0; i < subCategories.size(); i++) {
                if (header.order() < fieldList.get(i).getAnnotation(Header.class).order()) {
                    subCategories.add(i, subCategory);
                    found = true;
                    break;
                }
            }
            if (!found) {
                subCategories.add(subCategory);
            }
        }

        // 构建第一行和第二行的内容
        for (Map.Entry<String, List<String>> entry : categoryMap.entrySet()) {
            List<String> subCategories = entry.getValue();
            String category = entry.getKey();

            firstRow.add(category);
            secondRow.add(subCategories.isEmpty() ? "" : subCategories.get(0));

            for (int i = 1; i < subCategories.size(); i++) {
                firstRow.add("");
                secondRow.add(subCategories.get(i));
            }
        }

        headerList.add(firstRow);
        headerList.add(secondRow);

        return headerList;
    }

    public static void setHeadersStyle(List<List<String>> headRows, ExcelWriter writer) {
        if (headRows == null || headRows.isEmpty()) return;

        CellStyle cellStyle = writer.getCellStyle();
        cellStyle.setWrapText(true);

        Font font = writer.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        cellStyle.setFont(font);
        writer.setColumnWidth(3, 35);

        for (int i = 0; i < headRows.get(0).size(); i++) {
            if (i == 3) {
                writer.setColumnWidth(i, 35);
            } else {
                writer.setColumnWidth(i, 15);
            }
        }


        // 设置表头样式
        writer.merge(0, 0, 8, 13, "办理基本情况", cellStyle);
        writer.merge(0, 0, 14, 19, "办理效果", cellStyle);
        writer.merge(0, 0, 20, 25, "办理质量评价", cellStyle);
        for (int index = 0; index < 8; index++) {
            writer.merge(0, 1, index, index, headRows.get(0).get(index), cellStyle);
        }

        writer.merge(0, 1, 26, 26, "其他意见", cellStyle);

        // 写入表头
        writer.write(headRows, true);
        // 设置行高
        writer.setRowHeight(0, 30);
        writer.setRowHeight(1, 30);

    }

}
