package com.ruoyi.project.committee.resumption.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 公益情况分页视图对象
 */
@Data
@ApiModel("公益情况分页视图对象")
public class CommunityPageVo {

    @ApiModelProperty("公益情况ID")
    private String id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("参与者名称")
    private String participantsName;

    @ApiModelProperty("社区类型名称")
    private String communityTypeName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("填报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("审核状态")
    private String auditStatus;

}
