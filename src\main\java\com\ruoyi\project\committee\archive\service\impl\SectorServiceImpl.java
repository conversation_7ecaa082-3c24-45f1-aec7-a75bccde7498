package com.ruoyi.project.committee.archive.service.impl;

import com.ruoyi.common.enums.committee.SectorType;
import com.ruoyi.project.committee.archive.converter.SectorConverter;
import com.ruoyi.project.committee.archive.service.ISectorService;
import com.ruoyi.project.committee.archive.domain.Sector;
import com.ruoyi.project.committee.archive.domain.vo.SectorVo;
import com.ruoyi.project.committee.archive.mapper.SectorMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SectorServiceImpl implements ISectorService {

    @Resource
    private SectorMapper sectorMapper;

    @Override
    public List<SectorVo> selectSectorList() {
        List<Sector> sectorList = sectorMapper.selectSectorList(null, SectorType.SUB_SECTOR);
        return SectorConverter.INSTANCE.convertTo(sectorList);
    }
}
