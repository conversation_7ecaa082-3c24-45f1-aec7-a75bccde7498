package com.ruoyi.project.proposal.service.impl;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.proposal.converter.ProposalHandleConverter;
import com.ruoyi.project.proposal.domain.ProposalUndertakeUnit;
import com.ruoyi.project.proposal.domain.dto.UndertakeUnitDto;
import com.ruoyi.project.proposal.domain.vo.UndertakeUnitVo;
import com.ruoyi.project.proposal.mapper.ProposalUndertakeUnitMapper;
import com.ruoyi.project.proposal.service.IProposalUndertakeUnitService;
import com.ruoyi.project.system.domain.SysDept;
import com.ruoyi.project.system.domain.SysUser;
import com.ruoyi.project.system.mapper.SysDeptMapper;
import com.ruoyi.project.system.mapper.SysUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProposalUndertakeUnitServiceImpl implements IProposalUndertakeUnitService {

    @Resource
    private ProposalUndertakeUnitMapper undertakeUnitMapper;

    @Resource
    private SysUserMapper sysUserMapper;


    @Override
    public void insertUndertakeUnit(Long handleId, List<UndertakeUnitDto> undertakeUnitDtoList) {

        if (ObjectUtil.isEmpty(undertakeUnitDtoList)) {
            return;
        }

        for (UndertakeUnitDto undertakeUnitDto : undertakeUnitDtoList) {
            ProposalUndertakeUnit proposalUndertakeUnit = new ProposalUndertakeUnit();
            proposalUndertakeUnit.setHandleId(handleId);
            proposalUndertakeUnit.setUnitId(Long.parseLong(undertakeUnitDto.getUnitId()));
            proposalUndertakeUnit.setUndertakeWay(undertakeUnitDto.getUndertakeWay());
            proposalUndertakeUnit.setDeadline(undertakeUnitDto.getDeadline());
            undertakeUnitMapper.insert(proposalUndertakeUnit);
        }

    }

    @Override
    public void updateUndertakeUnit(Long handleId, List<UndertakeUnitDto> undertakeUnitDtoList) {

        if (ObjectUtil.isEmpty(undertakeUnitDtoList)) {
            return;
        }
        undertakeUnitMapper.delete(new LambdaQueryWrapper<ProposalUndertakeUnit>()
                .eq(ProposalUndertakeUnit::getHandleId, handleId));
        this.insertUndertakeUnit(handleId, undertakeUnitDtoList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editUndertakeUnit(List<UndertakeUnitDto> undertakeUnitDtoList) {

        if (ObjectUtil.isNotEmpty(undertakeUnitDtoList)) {
            for (UndertakeUnitDto unitDto : undertakeUnitDtoList) {
                ProposalUndertakeUnit proposalUndertakeUnit = new ProposalUndertakeUnit();
                proposalUndertakeUnit.setId(Long.parseLong(unitDto.getId()));
                proposalUndertakeUnit.setUnitId(Long.parseLong(unitDto.getUnitId()));
                proposalUndertakeUnit.setUndertakeWay(unitDto.getUndertakeWay());
                proposalUndertakeUnit.setDeadline(unitDto.getDeadline());
                undertakeUnitMapper.updateById(proposalUndertakeUnit);
            }

        }

    }

    @Override
    public List<UndertakeUnitVo> getUndertakeUnitList(Long handleId) {
        List<UndertakeUnitVo> undertakeUnitVos = new ArrayList<>();
        List<ProposalUndertakeUnit> undertakeUnits = undertakeUnitMapper.selectList(new LambdaQueryWrapper<ProposalUndertakeUnit>()
                .eq(ProposalUndertakeUnit::getHandleId, handleId));
        if (ObjectUtil.isNotEmpty(undertakeUnits)) {
            List<String> unitIds = undertakeUnits.stream()
                    .map(item -> String.valueOf(item.getUnitId()))
                    .collect(Collectors.toList());

            List<SysUser> deptList = sysUserMapper.selectUserByIds(unitIds);
            Map<Long, String> deptMap = deptList.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getUserName));
            for (ProposalUndertakeUnit undertakeUnit : undertakeUnits) {
                UndertakeUnitVo undertakeUnitVo = new UndertakeUnitVo();
                undertakeUnitVo.setId(String.valueOf(undertakeUnit.getId()));
                undertakeUnitVo.setUnitId(String.valueOf(undertakeUnit.getUnitId()));
                undertakeUnitVo.setUnitName(deptMap.get(undertakeUnit.getUnitId()));
                undertakeUnitVo.setUndertakeWay(undertakeUnit.getUndertakeWay());
                undertakeUnitVo.setDeadline(undertakeUnit.getDeadline());
                undertakeUnitVo.setHandleStatus(undertakeUnit.getHandleStatus());
                undertakeUnitVos.add(undertakeUnitVo);
            }
        }



        return undertakeUnitVos;
    }

    @Override
    public List<ProposalUndertakeUnit> selectUndertakeUnitList(Long handleId) {
        return undertakeUnitMapper.selectList(new LambdaQueryWrapper<ProposalUndertakeUnit>()
                .eq(ProposalUndertakeUnit::getHandleId, handleId));
    }

    @Override
    public void deleteUndertakeUnit(Long handleId, List<UndertakeUnitDto> unitList) {
        if (ObjectUtil.isNotEmpty(unitList)) {
            List<Long> idList = unitList.stream().map(item -> Long.parseLong(item.getId())).collect(Collectors.toList());
            undertakeUnitMapper.delete(new LambdaQueryWrapper<ProposalUndertakeUnit>()
                    .eq(ProposalUndertakeUnit::getHandleId, handleId)
                    .eq(ProposalUndertakeUnit::getHandleStatus, false)
                    .notIn(ProposalUndertakeUnit::getId, idList)
            );
        } else {
            undertakeUnitMapper.delete(new LambdaQueryWrapper<ProposalUndertakeUnit>()
                    .eq(ProposalUndertakeUnit::getHandleId, handleId)
            );
        }
    }
}
