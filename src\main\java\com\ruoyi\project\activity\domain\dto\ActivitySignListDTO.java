package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 活动签到列表查询DTO
 */
@Data
@ApiModel(value = "活动签到列表查询DTO")
public class ActivitySignListDTO {

    /**
     * 活动ID
     */
    @NotBlank(message = "活动ID不能为空")
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityPkid;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer pageNum = 1;

    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer pageSize = 10;

    /**
     * 人员姓名（模糊查询）
     */
    @ApiModelProperty(value = "人员姓名（模糊查询）")
    private String pepoleName;
}
