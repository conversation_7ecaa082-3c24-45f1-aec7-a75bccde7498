package com.ruoyi.common.enums;

import lombok.Getter;

@Getter
public enum SexEnum {

    MALE("1", "男"),
    FEMALE("0", "女");

    private final String code;
    private final String desc;

    SexEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code) {
        for (SexEnum sexEnum : SexEnum.values()) {
            if (sexEnum.getCode().equals(code)) {
                return sexEnum.getDesc();
            }
        }
        return null;
    }
}
