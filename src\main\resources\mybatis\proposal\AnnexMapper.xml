<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.proposal.mapper.AnnexMapper">

    <select id="selectAnnexByProposalId" resultType="annexVo">
        SELECT
            a.id AS id,
            a.annex_name AS annexName,
            a.annex_type AS annexType,
            a.url AS url
        FROM proposal_annex_rel pal
            LEFT JOIN annex a ON a.id = pal.annex_id
        WHERE pal.proposal_id = #{proposalId}
    </select>
</mapper>