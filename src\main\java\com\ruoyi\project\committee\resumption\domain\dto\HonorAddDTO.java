package com.ruoyi.project.committee.resumption.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 荣誉新增DTO
 */
@Data
@ApiModel("荣誉新增DTO")
public class HonorAddDTO {

    @NotBlank(message = "标题不能为空")
    @ApiModelProperty("标题")
    private String title;

    @NotBlank(message = "获奖时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("填报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date collectTime;

    @NotBlank(message = "获奖类型不能为空")
    @ApiModelProperty("获奖类型")
    private String honorType;

    @NotBlank(message = "获奖等级不能为空")
    @ApiModelProperty("获奖等级")
    private String honorLevel;

    @ApiModelProperty("区县级")
    private Integer districtLevel = 0;

    @ApiModelProperty("市级")
    private Integer cityLevel = 0;

    @ApiModelProperty("省级")
    private Integer provinceLevel = 0;

    @ApiModelProperty("国家级")
    private Integer nationalLevel = 0;

    @NotBlank(message = "备注不能为空")
    @ApiModelProperty("备注")
    private String remark;

    @NotBlank(message = "参与人不能为空")
    @ApiModelProperty("参与人")
    private String participantsName;

    @ApiModelProperty("参与人列表")
    private List<String> participantList;

    @ApiModelProperty("附件列表")
    private List<HonorAnnexDTO> annexList;

    @ApiModelProperty("状态：0-暂存，1-提交")
    private String status;
}
