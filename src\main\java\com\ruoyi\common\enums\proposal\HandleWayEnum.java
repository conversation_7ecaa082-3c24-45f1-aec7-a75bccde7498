package com.ruoyi.common.enums.proposal;

import lombok.Getter;

@Getter
public enum HandleWayEnum {

    /**
     * 办理方式枚举
     * 1、面谈
     * 2、电话沟通
     * 3、调研
     * 4、召开座谈会
     * 5、文来文往
     * 6、未联系
     */

    FACE_TALK("面谈"),
    PHONE_TALK("电话沟通"),
    RESEARCH("调研"),
    MEETING("召开座谈会"),
    WRITE_TO_WRITE("文来文往"),
    UN_CONTACTED("未联系");

    private final String description;

    HandleWayEnum(String description) {
        this.description = description;
    }


    public static String getDescription(String value) {
        for (HandleWayEnum handleWayEnum : HandleWayEnum.values()) {
            if (handleWayEnum.name().equals(value)) {
                return handleWayEnum.getDescription();
            }
        }
        return null;
    }
}
