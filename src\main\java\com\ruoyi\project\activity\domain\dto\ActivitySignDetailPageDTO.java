package com.ruoyi.project.activity.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 活动签到明细分页查询DTO
 */
@Data
@ApiModel(value = "活动签到明细分页查询DTO")
public class ActivitySignDetailPageDTO {

    /**
     * 签到ID
     */
    @NotBlank(message = "签到ID不能为空")
    @ApiModelProperty(value = "签到ID", required = true)
    private String signPkid;
    
    /**
     * 人员姓名（模糊查询）
     */
    @ApiModelProperty(value = "人员姓名（模糊查询）")
    private String peopleName;
    
    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer currentPage = 1;
    
    /**
     * 每页记录数
     */
    @ApiModelProperty(value = "每页记录数", example = "10")
    private Integer pageSize = 10;
}
