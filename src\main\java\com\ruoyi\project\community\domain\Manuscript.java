package com.ruoyi.project.community.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.enums.manuscript.*;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 社区民意信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Manuscript extends BaseEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 编号 */
    private String code;

    /** 年度 */
    private Integer year;

    /** 标题 */
    private String title;

    /** 稿件类别 */
    private CategoryEnum category;

    /** 类别明细 */
    private CategoryDetailEnum categoryDetail;

    /** 反映单位 */
    private String reflectUnit;

    /** 反映人 */
    private String reflector;

    /** 联系人 */
    private String contact;

    /** 稿件状态 */
    private ManuscriptStatusEnum status;

    /** 签发期数 */
    private String issueNumber;

    /** 信息来源 */
    private SourceEnum source;

    /** 采用方式 */
    private AdoptWayEnum adoptWay;

    /** 来稿方式 */
    private String submitWay;

    /** 主送单位 */
    private String recipient;

    /** 抄送单位 */
    private String ccUnit;

    /** 报送类别 */
    private ReportTypeEnum reportType;

    /** 报送对象 */
    private String reportTarget;

    /** 其他报送对象 */
    private String otherReportTarget;

    /** 报送单位 */
    private String reportUnit;

    /** 报送单位明细 */
    private String reportUnitDetail;

    /** 抄送 */
    private String carbonCopy;

    /** 是否公开 */
    private Boolean isPublish;

    /** 是否反馈 */
    private Boolean isFeedback;

    /** 是否批示 */
    private Boolean isEndorsed;

    /** 来稿时间 */
    private Date submissionTime;
}
