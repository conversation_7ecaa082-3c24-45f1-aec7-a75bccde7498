package com.ruoyi.project.committee.archive.service;

import com.ruoyi.project.committee.archive.domain.ArchiveDept;

import java.util.List;

/**
 * 部门管理 服务层
 */
public interface IArchiveDeptService {
    /**
     * 查询部门管理数据
     * 
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<ArchiveDept> selectDeptList(ArchiveDept dept);

    /**
     * 根据祖级列表查询部门
     * 
     * @param ancestors 祖级列表
     * @return 部门列表
     */
    public List<ArchiveDept> selectDeptListByAncestors(String ancestors);

    /**
     * 根据部门ID查询信息
     * 
     * @param deptId 部门ID
     * @return 部门信息
     */
    public ArchiveDept selectDeptById(Long deptId);
}
