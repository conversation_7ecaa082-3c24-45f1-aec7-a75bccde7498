package com.ruoyi.project.proposal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalHandleOrganizer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;
import java.util.Map;

@Mapper
public interface ProposalHandleOrganizerMapper extends BaseMapper<ProposalHandleOrganizer> {

    Integer insertProposalHandleOrganizer(@Param("list") List<ProposalHandleOrganizer> organizerList);


    List<Map<String, Object>> selectOrganizerList(@Param("handleIdList") List<String> handleIdList);
}
