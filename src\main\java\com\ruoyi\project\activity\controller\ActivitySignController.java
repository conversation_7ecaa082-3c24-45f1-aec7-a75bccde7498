package com.ruoyi.project.activity.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.activity.domain.ActivitySign;
import com.ruoyi.project.activity.domain.dto.ActivitySignAddDTO;
import com.ruoyi.project.activity.domain.dto.ActivitySignListDTO;
import com.ruoyi.project.activity.domain.vo.ActivitySignVO;
import com.ruoyi.project.activity.service.IActivitySignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 活动签到控制器
 */
@Api(tags = "活动签到管理")
@RestController
@RequestMapping("/activity/sign")
public class ActivitySignController extends BaseController {

    @Autowired
    private IActivitySignService activitySignService;

    /**
     * 获取签到列表
     */
    @PreAuthorize("@ss.hasPermi('activity:sign:list')")
    @GetMapping("/list")
    public TableDataInfo list(ActivitySign activitySign) {
        startPage();
        LambdaQueryWrapper<ActivitySign> queryWrapper = new LambdaQueryWrapper<>();

        if (activitySign.getActivityPkid() != null && !activitySign.getActivityPkid().isEmpty()) {
            queryWrapper.eq(ActivitySign::getActivityPkid, activitySign.getActivityPkid());
        }

        if (activitySign.getPepolePkid() != null && !activitySign.getPepolePkid().isEmpty()) {
            queryWrapper.eq(ActivitySign::getPepolePkid, activitySign.getPepolePkid());
        }

        // 按签到开始时间降序排列
        queryWrapper.orderByDesc(ActivitySign::getSignBeginDate);

        List<ActivitySign> list = activitySignService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 获取签到详细信息
     */
    @PreAuthorize("@ss.hasPermi('activity:sign:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return success(activitySignService.getById(id));
    }

    /**
     * 获取某个活动的所有签到记录
     */
    @PreAuthorize("@ss.hasPermi('activity:sign:list')")
    @GetMapping("/activity/{activityPkid}")
    public AjaxResult getSignsByActivity(@PathVariable String activityPkid) {
        LambdaQueryWrapper<ActivitySign> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySign::getActivityPkid, activityPkid);
        queryWrapper.orderByDesc(ActivitySign::getSignBeginDate);
        return success(activitySignService.list(queryWrapper));
    }

    /**
     * 获取某个人员的所有签到记录
     */
    @PreAuthorize("@ss.hasPermi('activity:sign:list')")
    @GetMapping("/people/{pepolePkid}")
    public AjaxResult getSignsByPeople(@PathVariable String pepolePkid) {
        LambdaQueryWrapper<ActivitySign> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivitySign::getPepolePkid, pepolePkid);
        queryWrapper.orderByDesc(ActivitySign::getSignBeginDate);
        return success(activitySignService.list(queryWrapper));
    }

    /**
     * 删除签到
     */
    @PreAuthorize("@ss.hasPermi('activity:sign:remove')")
    @Log(title = "签到管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(activitySignService.removeByIds(Arrays.asList(ids)));
    }

    /**
     * 根据活动ID获取签到列表（分页）
     */
    @ApiOperation(value = "根据活动ID获取签到列表（分页）")
    @PreAuthorize("@ss.hasPermi('activity:sign:list')")
    @PostMapping("/listByActivityId")
    public TableDataInfo getSignListByActivityId(@RequestBody @Validated ActivitySignListDTO dto) {
        IPage<ActivitySignVO> page = activitySignService.getSignListByActivityId(dto);
        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 新增活动签到信息
     */
    @ApiOperation(value = "新增活动签到信息")
    @PreAuthorize("@ss.hasPermi('activity:sign:add')")
    @Log(title = "活动签到管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addActivitySign(@RequestBody @Validated ActivitySignAddDTO dto) {
        return toAjax(activitySignService.addActivitySign(dto));
    }
}