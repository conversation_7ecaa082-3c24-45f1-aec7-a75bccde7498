package com.ruoyi.project.committee.meeting.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.committee.meeting.domain.MeetingDoc;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingDocPageDto;

public interface MeetingDocMapper extends BaseMapper<MeetingDoc> {

    default IPage<MeetingDoc> selectMeetingDoc(Page<MeetingDoc> page, MeetingDocPageDto pageDto) {
        return selectPage(page, new LambdaQueryWrapper<MeetingDoc>()
                .eq(MeetingDoc::getMeetingId, pageDto.getMeetingId())
                .like(ObjectUtil.isNotEmpty(pageDto.getTitle()), MeetingDoc::getTitle, pageDto.getTitle())
                .eq(ObjectUtil.isNotEmpty(pageDto.getIsPublicly()), MeetingDoc::getIsPublicly, pageDto.getIsPublicly())
                .eq(ObjectUtil.isNotEmpty(pageDto.getDocType()), MeetingDoc::getDocType, pageDto.getDocType())
                .orderByDesc(MeetingDoc::getCreateTime)
        );
    }
}
