package com.ruoyi.project.committee.archive.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 委员查询结果视图对象
 */
@Data
public class CommitteeMemberQueryVo {

    @ApiModelProperty(value = "ID")
    private String id;

    @ApiModelProperty(value = "委员ID")
    private String userId;

    @ApiModelProperty(value = "委员姓名")
    private String userName;

    @ApiModelProperty(value = "职务")
    private String unitPost;
}
