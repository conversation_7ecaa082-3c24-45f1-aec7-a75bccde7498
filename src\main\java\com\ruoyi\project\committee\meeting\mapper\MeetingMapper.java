package com.ruoyi.project.committee.meeting.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.project.committee.meeting.domain.MeetingInfo;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingPageDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingInfoPageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper
public interface MeetingMapper extends BaseMapper<MeetingInfo> {

    default IPage<MeetingInfo> getMeetingPage(Page<MeetingInfo> page, MeetingPageDto pageDto) {
        return selectPage(page, new LambdaQueryWrapper<MeetingInfo>()
                .like(ObjectUtil.isNotNull(pageDto.getMeetingName()), MeetingInfo::getMeetingName, pageDto.getMeetingName())
                .eq(ObjectUtil.isNotNull(pageDto.getMeetingType()), MeetingInfo::getMeetingType, pageDto.getMeetingType())
                .eq(ObjectUtil.isNotNull(pageDto.getIsPublish()), MeetingInfo::getIsPublish, pageDto.getIsPublish())
                .ge(ObjectUtil.isNotNull(pageDto.getStartTime()), MeetingInfo::getStartTime, pageDto.getStartTime())
                .le(ObjectUtil.isNotNull(pageDto.getEndTime()), MeetingInfo::getEndTime, pageDto.getEndTime())
                .orderByDesc(MeetingInfo::getCreateTime)
        );
    }

    default IPage<MeetingInfo> getMyPage(Page<MeetingInfo> page, MeetingPageDto pageDto, Set<Long> personIds) {
        return selectPage(page, new LambdaQueryWrapper<MeetingInfo>()
                .like(ObjectUtil.isNotNull(pageDto.getMeetingName()), MeetingInfo::getMeetingName, pageDto.getMeetingName())
                .eq(ObjectUtil.isNotNull(pageDto.getMeetingType()), MeetingInfo::getMeetingType, pageDto.getMeetingType())
                .ge(ObjectUtil.isNotNull(pageDto.getStartTime()), MeetingInfo::getStartTime, pageDto.getStartTime())
                .le(ObjectUtil.isNotNull(pageDto.getEndTime()), MeetingInfo::getEndTime, pageDto.getEndTime())
                .exists("SELECT 1 FROM meeting_participants mp WHERE mp.meeting_id = meeting_info.id AND mp.people_id IN (" +
                        personIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")")
                .orderByDesc(MeetingInfo::getCreateTime)
        );
    }

    IPage<MeetingInfoPageVo> getMyMeetingPage(@Param("page") Page<MeetingInfo> page,
                                              @Param("pageDto") MeetingPageDto pageDto,
                                              @Param("userId") Long userId);


    default Integer changePublishStatus(List<String> idList, boolean isPublish) {
        return update(null, new LambdaUpdateWrapper<MeetingInfo>()
                .set(MeetingInfo::getIsPublish, isPublish)
                .in(MeetingInfo::getId, idList)
        );
    };
}
