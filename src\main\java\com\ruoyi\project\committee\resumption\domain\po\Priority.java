package com.ruoyi.project.committee.resumption.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.enums.AuditStatusEnum;
import com.ruoyi.common.enums.committee.PriorityTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * 重点工作信息对象 t_priority
 */
@Data
@TableName("t_priority")
public class Priority {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 标题 */
    private String title;

    /** 参与时间 */
    private Date participationTime;

    /** 填报时间 */
    private Date collectTime;

    /** 备注 */
    private String remark;

    /** 发布人ID */
    private String publishId;

    /** 重点工作类型 */
    private PriorityTypeEnum priorityType;

    /** 参与者名称 */
    private String participantsName;

    /** 作者名称 */
    private String writerName;

    /** 审核时间 */
    private Date checkTime;

    /** 审核人 */
    private String checker;

    /** 审核人电话 */
    private String checkerPhone;

    /** 审核原因 */
    private String checkReason;

    /** 审核状态 */
    private String auditStatus;

    /** 状态 */
    private String status;

    /*** 附件 */
    private String annex;

    /** 创建人ID */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人ID */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 逻辑删除标志位 */
    @TableLogic
    private Boolean delFlag;
}
