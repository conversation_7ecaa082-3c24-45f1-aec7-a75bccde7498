package com.ruoyi.project.committee.resumption.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 重点工作信息VO
 */
@Data
@ApiModel(value = "重点工作信息VO", description = "重点工作信息视图对象")
public class PriorityVo {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "标题")
    private String title;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "参与时间")
    private Date participationTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "填报时间")
    private Date collectTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "发布人ID")
    private String publishId;

    @ApiModelProperty(value = "重点工作类型")
    private String priorityType;

    @ApiModelProperty(value = "参与者名称")
    private String participantsName;

    @ApiModelProperty(value = "作者名称")
    private String writerName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "审核时间")
    private Date checkTime;

    @ApiModelProperty(value = "审核人")
    private String checker;

    @ApiModelProperty(value = "审核人电话")
    private String checkerPhone;

    @ApiModelProperty(value = "审核原因")
    private String checkReason;

    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "特别突出")
    private List<ParticipantVo> outstandingList;

    @ApiModelProperty(value = "表现良好")
    private List<ParticipantVo> goodList;

    @ApiModelProperty(value = "有所参与")
    private List<ParticipantVo> participantList;

    @ApiModelProperty(value = "附件列表")
    private List<AnnexVo> annexList;

}
