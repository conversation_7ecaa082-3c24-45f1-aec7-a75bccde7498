package com.ruoyi.project.committee.meeting.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class MeetingInfoPageVo {

    private String id;

    private String participantId;

    @ApiModelProperty(value = "会议名称")
    private String meetingName;

    @ApiModelProperty(value = "会议类型")
    private String meetingType;

    @ApiModelProperty(value = "是否发布")
    private Boolean isPublish;

    @ApiModelProperty(value = "会议时间")
    private String meetingTime;

    @ApiModelProperty(value = "会议开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "会议结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "会议地址")
    private String meetingAddress;

    @ApiModelProperty(value = "是否参加")
    private Boolean isAttend;

}
