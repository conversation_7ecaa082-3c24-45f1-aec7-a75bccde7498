package com.ruoyi.project.committee.meeting.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.committee.meeting.domain.MeetingParticipants;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingAttendInfoDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingParticipantSearchDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingParticipantsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会议参与委员信息 Mapper 接口
 */
@Mapper
public interface MeetingParticipantsMapper extends BaseMapper<MeetingParticipants> {

    List<MeetingParticipantsVo> getMeetingParticipantsList(@Param("searchDto") MeetingParticipantSearchDto searchDto);

    List<MeetingParticipantsVo> getParticipantsListByMeetingId(Long meetingId);

    MeetingParticipants getMeetingParticipantsByUserId(Long meetingId, Long userId);

    default List<MeetingParticipants> getParticipantsByMeetingId(Long meetingId) {
        return selectList(new LambdaQueryWrapper<MeetingParticipants>()
                .eq(MeetingParticipants::getMeetingId, meetingId));
    }

    default void deleteBtMeetingId(Long meetingId) {
        this.delete(new LambdaQueryWrapper<MeetingParticipants>()
                .eq(MeetingParticipants::getMeetingId, meetingId));
    }

    default Integer saveAttendInfo(MeetingAttendInfoDto attendDto) {
        return this.update(null, new LambdaUpdateWrapper<MeetingParticipants>()
                .set(MeetingParticipants::getIsAttend, attendDto.getIsAttend())
                .set(!attendDto.getIsAttend(), MeetingParticipants::getNoAttendReason, attendDto.getReason())
                .eq(MeetingParticipants::getId, attendDto.getParticipantId())
        );
    };
}