package com.ruoyi.project.committee.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 会议签到操作DTO
 */
@Data
public class MeetingSignOperateDto {

    @NotEmpty(message = "签到详情Id列表")
    @ApiModelProperty(value = "签到详情ID列表")
    private List<String> signDetailIdList;

    @ApiModelProperty(value = "请假理由")
    private String reason;
}
