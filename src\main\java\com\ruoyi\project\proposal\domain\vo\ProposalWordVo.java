package com.ruoyi.project.proposal.domain.vo;

import lombok.Data;

import java.util.Date;

@Data
public class ProposalWordVo {

    /**
     * TODO: 部分字段待确定来源
     * category 分类
     * distinct 地区
     * title 标题
     * censorOpinion 审核意见
     */

    private String category;

    private String registerDate;

    private String distinct;

    private String title;

    private String caseReason;

    private String serialNumber;

    private String censorOpinion;

    private String caseContent;

    // 提案人信息
    private String casePerson;

    private String position;

    private String address;

    private String telephone;

    private String postalCode;

    private String email;

    // 附议人信息
    private String secondedPerson;
}
