package com.ruoyi.project.proposal.domain.vo;

import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 承办单位 Dto
 */
@Data
public class UndertakeUnitVo {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "承办单位id")
    private String unitId;

    @ApiModelProperty(value = "承办单位名称")
    private String unitName;

    @ApiModelProperty(value = "承办方式")
    private UndertakeWayEnum undertakeWay;

    @ApiModelProperty(value = "办理截止日期")
    private Date deadline;

    @ApiModelProperty(value = "办理状态")
    private Boolean handleStatus;
}
