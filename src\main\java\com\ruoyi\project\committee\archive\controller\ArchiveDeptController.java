package com.ruoyi.project.committee.archive.controller;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.committee.archive.domain.ArchiveDept;
import com.ruoyi.project.committee.archive.service.IArchiveDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 部门信息
 */
@RestController
@RequestMapping("/committee/archive/dept")
public class ArchiveDeptController extends BaseController {
    @Autowired
    private IArchiveDeptService deptService;

    /**
     * 获取部门列表
     */
    @GetMapping("/list")
    public AjaxResult list(ArchiveDept dept) {
        return success(deptService.selectDeptList(dept));
    }

    /**
     * 根据祖级列表查询部门
     */
    @GetMapping("/listByAncestors/{ancestors}")
    public AjaxResult listByAncestors(@PathVariable("ancestors") String ancestors) {
        return success(deptService.selectDeptListByAncestors(ancestors));
    }

    /**
     * 根据部门编号获取详细信息
     */
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId) {
        return success(deptService.selectDeptById(deptId));
    }
}
