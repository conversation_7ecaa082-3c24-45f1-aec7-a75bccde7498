package com.ruoyi.project.proposal.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalFeedback;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.proposal.domain.vo.ProposalFeedbackVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;

@Mapper
public interface ProposalFeedbackMapper extends BaseMapper<ProposalFeedback> {

    List<ProposalFeedbackVo> selectProposalFeedbackList(@Param("proposalId") String proposalId);

    List<AnnexVo> selectProposalFeedbackAnnexList(@Param("feedbackId") String feedbackId);

    default ProposalFeedback selectUnitFeedbackById(String proposalId, Long unitId) {
        return selectOne(new LambdaQueryWrapper<ProposalFeedback>()
                .eq(ProposalFeedback::getProposalId, proposalId)
                .eq(ProposalFeedback::getReplyUserId, unitId)
        );
    }

}
