package com.ruoyi.project.proposal.domain.vo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProposalReceptionStatisticsVo {

    @ApiModelProperty(value = "承办单位")
    private String unitName;

    @ApiModelProperty(value = "总数")
    private Long total;

    @ApiModelProperty(value = "案号")
    private String caseNumber;

    @ApiModelProperty(value = "案由")
    private String caseReason;

    @ApiModelProperty(value = "接收情况")
    private String reception;

    @JsonIgnore
    private String undertakeWay;

}
