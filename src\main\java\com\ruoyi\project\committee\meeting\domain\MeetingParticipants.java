package com.ruoyi.project.committee.meeting.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会议参与委员信息对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meeting_participants")
public class MeetingParticipants extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 会议id
     */
    private Long meetingId;

    /**
     * 委员id
     */
    private Long peopleId;

    /**
     * 是否参加
     */
    private Boolean isAttend;

    /**
     * 不参加理由
     */
    private String noAttendReason;

    /**
     * 逻辑删除标志位
     */
    @TableLogic
    private Boolean delFlag;


    @TableField(exist = false)
    private String remark;
}