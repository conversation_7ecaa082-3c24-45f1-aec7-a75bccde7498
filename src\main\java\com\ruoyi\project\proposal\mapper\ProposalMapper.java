package com.ruoyi.project.proposal.mapper;

import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.project.proposal.domain.Proposal;
import com.ruoyi.project.proposal.domain.ProposalMerge;
import com.ruoyi.project.proposal.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 提案信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Mapper
public interface ProposalMapper extends BaseMapper<Proposal> {

    IPage<ProposalPageVo> selectProposalPage(@Param("page") Page<ProposalPageVo> page,
                                             @Param("pageParam") ProposalPageParamVo pageParam,
                                             @Param("caseFilingList") List<CaseFillingEnum> caseFillingList);

    IPage<ProposalAssignPageVo> selectProposalAssignPage(@Param("page")  Page<ProposalAssignPageVo> page,
                                                         @Param("pageParam") ProposalPageParamVo pageParam);

    IPage<ProposalComprehensivePageVo> selectComprehensiveProposalPage(@Param("page") Page<ProposalComprehensivePageVo> page,
                                                                       @Param("pageParam") ProposalPageParamVo pageParam,
                                                                       @Param("ids") Set<String> ids);


    ProposalWordVo selectProposalWordVoById(@Param("id") String id);


    Integer batchUpdateProposalAssignStatus(@Param("ids") String[] ids);

    ProposalComprehensiveVo selectComprehensiveInfoById(String id);

    Integer batchDeleteProposal(@Param("ids") List<String> ids);

    List<Map<String, Object>> getProposerMap(@Param("proposalIdList") List<String> proposalIdList);

    IPage<SupervisionPageVo> getSupervisionPage(@Param("page") Page<SupervisionPageVo> page,
                                                @Param("pageParam") ProposalPageParamVo pageParam);

    default Integer urge(String id) {
        return update(null, new UpdateWrapper<Proposal>()
                .eq("id", id)
                .setSql("urge_count = urge_count + 1"));
    }

    Long getProposalCount(Integer year);


    IPage<ProposalComprehensivePageVo> getUserProposal(@Param("page")  Page<ProposalComprehensivePageVo> page,
                                                       @Param("pageParam") ProposalPageParamVo pageParam,
                                                       @Param("proposerRelId") Long proposerRelId);

    Integer selectSerialNumber();

    Integer selectCaseNumber();

    default void changeCaseFiling(String proposalId, CaseFillingEnum caseFillingEnum) {
        update(null, new LambdaUpdateWrapper<Proposal>()
                .eq(Proposal::getId, proposalId)
                .set(Proposal::getCaseFiling, caseFillingEnum)
        );
    };


    void mergeProposal(@Param("proposalId") String proposalId,
                       @Param("caseNumber") Integer caseNumber,
                       @Param("caseReason") String caseReason,
                       @Param("caseFiling") CaseFillingEnum caseFilling);

    default Boolean existsByCaseNumber(Integer caseNumber) {
        return exists(new LambdaQueryWrapper<Proposal>()
                .eq(Proposal::getCaseNumber, caseNumber)
                .eq(Proposal::getYear, DateUtil.year(DateUtil.date()))
        );
    };

    ProposalMerge getMergeInfo(String proposalId);

    default void mergeHandle(List<String> proposalIds) {
        update(null, new LambdaUpdateWrapper<Proposal>()
                .set(Proposal::getCaseFiling, CaseFillingEnum.FINISH)
                .in(Proposal::getId, proposalIds)
        );
    }
}
