package com.ruoyi.project.committee.meeting.service;

import com.ruoyi.project.committee.meeting.domain.dto.MeetingAttendInfoDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingParticipantSearchDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingParticipantsVo;

import java.util.List;

/**
 * 会议参与委员信息 Service接口
 */
public interface IMeetingParticipantsService {

    /**
     * 获取会议参与委员信息
     * @param searchDto 查询dto
     * @return 会议参与委员信息
     */
    List<MeetingParticipantsVo> getMeetingParticipantsList(MeetingParticipantSearchDto searchDto);

    /**
     * 获取会议参与委员姓名
     * @param meetingId 会议ID
     * @return 参与委员姓名
     */
    String getMeetingParticipants(String meetingId);

    /**
     * 创建会议参与委员信息
     *
     * @param meetingId    会议ID
     * @param peopleIdList 委员ID列表
     */
    void createParticipants(Long meetingId, List<String> peopleIdList);

    /**
     * 重置会议参与委员信息
     * @param meetingId    会议ID
     * @param peopleIdList 委员ID列表
     */
    void resetParticipants(Long meetingId, List<String> peopleIdList);


    /**
     * 保存会议参加情况
     * @param attendDto 参会信息
     */
    Boolean saveAttendInfo(MeetingAttendInfoDto attendDto);
}