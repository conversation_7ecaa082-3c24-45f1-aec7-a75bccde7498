<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.activity.mapper.ActivityNotificationReceptionMapper">
    <!-- 分页查询活动通知接收情况信息列表 -->
    <select id="selectNotificationReceptionPage" resultType="com.ruoyi.project.activity.domain.vo.ActivityNotificationReceptionVO">
        SELECT
	        anr.notification_id,
	        anr.recipient_id,
	        anr.receive_time,
	        anr.receive_status,
	        scm.user_name AS recipient_name,
            scm.number_id AS recipient_no
        FROM
	        activity_notification_reception anr
	    LEFT JOIN sys_committee_member scm ON anr.recipient_id = scm.id
        WHERE
            anr.del_flag = 0
            AND anr.notification_id = #{searchDto.notificationId}
        <if test="searchDto.recipientName != null and searchDto.recipientName != ''">
            AND scm.user_name LIKE CONCAT('%', #{searchDto.recipientName}, '%')
        </if>
        <if test="searchDto.isReaded != null">
            AND anr.receive_status = #{searchDto.isReaded}
        </if>
        ORDER BY
            scm.number_id
    </select>
    <!-- 批量插入（存在则更新） -->
    <insert id="saveOrUpdateBatch" useGeneratedKeys="true">
        INSERT INTO activity_notification_reception (notification_id, recipient_id, receive_status, create_by, create_at, del_flag)
        VALUES
        <foreach collection="entities" item="item" separator=",">
            (#{item.notificationId}, #{item.recipientId}, #{item.receiveStatus}, #{item.createBy}, #{item.createAt}, #{item.delFlag})
        </foreach>
        ON DUPLICATE KEY UPDATE
            update_by = VALUES(update_by),
            update_at = VALUES(update_at),
            del_flag = VALUES(del_flag)
    </insert>
    <!-- 通过通知ids批量删除(逻辑删除) -->
    <update id="deleteByNotificationIds">
        UPDATE activity_notification_reception
        SET 
            del_flag = 1,
            update_by = #{updateBy},
            update_at = NOW()
        WHERE
            notification_id IN
        <foreach collection="notificationIds" item="notificationId" open="(" separator="," close=")">
                #{notificationId}
        </foreach>
            AND del_flag = 0
    </update>
    <select id="selectReceptionListByNotificationId" resultType="com.ruoyi.project.activity.domain.vo.ActivityNotificationReceptionVO">
        SELECT
            anr.notification_id as notificationId,
            anr.recipient_id as recipientId,
            su.nick_name as recipientName,
            sd.dept_name as recipientDept,
            anr.receive_time as receiveTime,
            anr.receive_status as receiveStatus
        FROM
            activity_notification_reception anr
        LEFT JOIN sys_user su ON anr.recipient_id = su.user_id
        LEFT JOIN sys_dept sd ON su.dept_id = sd.dept_id
        WHERE
            anr.notification_id = #{notificationId}
            AND anr.del_flag = 0
        ORDER BY
            anr.create_at DESC
    </select>
    <update id="logicDeleteByNotificationId">
        UPDATE activity_notification_reception
        SET 
            del_flag = 1,
            update_by = #{updateBy},
            update_at = NOW()
        WHERE
            notification_id = #{notificationId}
            AND del_flag = 0
    </update>
    <update id="resetReceiveStatusByNotificationIdAndRecipientIds">
        UPDATE activity_notification_reception
        SET 
            receive_status = 0,
            update_by = #{updateBy},
            update_at = NOW()
        WHERE
            notification_id = #{notificationId}
            AND recipient_id IN
        <foreach collection="recipientIds" item="recipientId" open="(" separator="," close=")">
                #{recipientId}
            </foreach>
            AND del_flag = 0
    </update>
    <select id="selectNotificationIdsByActivityId" resultType="java.lang.Long">
        SELECT id
        FROM activity_notification
        WHERE activity_id = #{activityId}
        AND del_flag = 0
    </select>
    <update id="restoreDeletedByNotificationIdAndRecipientIds">
        UPDATE activity_notification_reception
        SET 
            del_flag = 0,
            update_by = #{updateBy},
            update_at = NOW()
        WHERE
            notification_id = #{notificationId}
            AND recipient_id IN
        <foreach collection="recipientIds" item="recipientId" open="(" separator="," close=")">
                #{recipientId}
            </foreach>
    </update>
    <update id="logicDeleteByNotificationIdAndRecipientIds">
        UPDATE activity_notification_reception
        SET 
            del_flag = 1,
            update_by = #{updateBy},
            update_at = NOW()
        WHERE
            notification_id = #{notificationId}
            AND recipient_id IN
        <foreach collection="recipientIds" item="recipientId" open="(" separator="," close=")">
                #{recipientId}
            </foreach>
            AND del_flag = 0
    </update>
</mapper>