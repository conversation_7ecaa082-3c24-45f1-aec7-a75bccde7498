package com.ruoyi.project.committee.resumption.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.committee.resumption.domain.po.CommunityAnnex;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 公益情况附件Mapper接口
 */
@Mapper
public interface CommunityAnnexMapper extends BaseMapper<CommunityAnnex> {

    /**
     * 根据公益情况ID查询附件列表
     *
     * @param communityId 公益情况ID
     * @return 附件列表
     */
    @Select("SELECT * FROM community_annex WHERE community_id = #{communityId}")
    List<CommunityAnnex> selectListByCommunityId(@Param("communityId") String communityId);
}
