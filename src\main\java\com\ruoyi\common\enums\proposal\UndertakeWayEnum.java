package com.ruoyi.common.enums.proposal;

import lombok.Getter;

/**
 * 承办方式枚举
 * 1、单办
 * 2、分办
 * 3、主办
 * 4、协办
 */
@Getter
public enum UndertakeWayEnum {

    SINGLE_OFFICE("单办"),

    JOINT_HANDLING("会办"),

    DISTRIBUTED_WORK("分办"),

    LEAD_OFFICE("主办"),

    ASSISTANT_OFFICE("协办"),

    ;


    private final String description;

    UndertakeWayEnum(String description) {
        this.description = description;
    }


    public static String getDescription(String name) {
        for (UndertakeWayEnum value : values()) {
            if (value.name().equals(name)) {
                return value.getDescription();
            }
        }
        return null;
    }
}
