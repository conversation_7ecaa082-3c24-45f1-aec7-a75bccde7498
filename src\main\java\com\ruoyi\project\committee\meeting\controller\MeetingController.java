package com.ruoyi.project.committee.meeting.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.framework.aspectj.lang.annotation.Anonymous;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.committee.meeting.domain.dto.*;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingDocPageVo;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingInfoPageVo;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingSignPageVo;
import com.ruoyi.project.committee.meeting.service.*;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 会议管理
 */
//@Anonymous
@RestController
@RequestMapping(value = "/meeting")
public class MeetingController extends BaseController {

    @Resource
    private IMeetingService meetingService;

    @Resource
    private IMeetingParticipantsService meetingParticipantsService;

    @Resource
    private IMeetingSignService meetingSignService;

    @Resource
    private IMeetingSignDetailService meetingSignDetailService;

    @Resource
    private IMeetingDocService meetingDocService;

    /**
     * 新增会议
     */
    @PostMapping("/create")
    public AjaxResult add(@Valid @RequestBody MeetingEditDto meetingEditDto) {
        return success(meetingService.createMeetingInfo(meetingEditDto));
    }

    /**
     * 修改会议
     */
    @PutMapping("/edit")
    public AjaxResult edit(@Valid @RequestBody MeetingEditDto meetingEditDto) {
        return success(meetingService.updateMeetingInfo(meetingEditDto));
    }

    /**
     * 删除会议
     */
    @DeleteMapping("/remove")
    public AjaxResult remove(@RequestParam("id") String id) {
        return toAjax(meetingService.deleteMeetingInfo(id));
    }

    /**
     * 发布会议信息
     */
    @PostMapping("/publish")
    public AjaxResult publish(@RequestBody List<String> idList) {
        return success(meetingService.publish(idList));
    }

    /**
     * 取消发布会议信息
     */
    @PostMapping("/unPublish")
    public AjaxResult unPublish(@RequestBody List<String> idList) {
        return success(meetingService.unPublish(idList));
    }

    /**
     * 会议查询
     * @return result
     */
    @PostMapping(value = "/page")
    public TableDataInfo getMeetingPage(@RequestBody MeetingPageDto pageDto) {
        IPage<MeetingInfoPageVo> pageResult = meetingService.getMeetingPage(pageDto);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 我的会议查询
     * @return result
     */
    @PostMapping("/myPage")
    public TableDataInfo getMyPage(@RequestBody MeetingPageDto pageDto) {
        IPage<MeetingInfoPageVo> pageResult = meetingService.getMyPage(pageDto);
        return getDataTable(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 获取会议详细信息
     */
    @GetMapping("/get")
    public AjaxResult getInfo(@RequestParam(value = "id") String id) {
        return success(meetingService.getMeetingById(id));
    }

    // --------------------------Meeting Participant Information--------------------------
    @PostMapping("/willAttend")
    public AjaxResult willAttend(@RequestBody MeetingAttendInfoDto attendDto) {
        return AjaxResult.success(meetingParticipantsService.saveAttendInfo(attendDto));
    }

    /**
     * 获取会议参加情况
     * @param searchDto 查询参数
     * @return 参加情况
     */
    @PostMapping("/getParticipants")
    public AjaxResult getParticipants(@RequestBody MeetingParticipantSearchDto searchDto) {
        return success(meetingParticipantsService.getMeetingParticipantsList(searchDto));
    }

    /**
     * 导出会议参加情况
     * @param id id
     * @param response response
     */
    @GetMapping("/exportParticipants")
    public void exportParticipants(@RequestParam(value = "id") String id, HttpServletResponse response) {
        meetingService.exportParticipants(id, response);
    }

    // --------------------------Meeting Sign Information--------------------------
    /**
     * 新增会议签到
     * @param createDto createDto
     * @return result
     */
    @PostMapping("/createSign")
    public AjaxResult createSign(@Valid @RequestBody MeetingSignEditDto createDto) {
        return success(meetingSignService.createMeetingSign(createDto));
    }

    /**
     * 获取会议签到情况
     * @param pageDto pageDto
     * @return result
     */
    @PostMapping("/getSignInfo")
    public TableDataInfo getSignInfo(@RequestBody MeetingSignPageDto pageDto) {
        IPage<MeetingSignPageVo> resultPage = meetingSignService.getMeetingSignPage(pageDto);
        return getDataTable(resultPage.getRecords(), resultPage.getTotal());
    }

    /**
     * 签到
     * @param operationDto operationDto
     * @return result
     */
    @PostMapping("/signIn")
    public AjaxResult signIn(@RequestBody MeetingSignOperateDto operationDto) {
        return success(meetingSignDetailService.batchSignIn(operationDto));
    }

    /**
     * 请假
     * @param operationDto operationDto
     * @return result
     */
    @PostMapping("/leave")
    public AjaxResult leave(@RequestBody MeetingSignOperateDto operationDto) {
        return success(meetingSignDetailService.leave(operationDto));
    }

    /**
     * 取消签到
     * @param operationDto operationDto
     * @return result
     */
    @PostMapping("/cancel")
    public AjaxResult cancel(@RequestBody MeetingSignOperateDto operationDto) {
        return success(meetingSignDetailService.cancel(operationDto));
    }

    /**
     * 获取会议签到情况(委员端)
     * @param pageDto pageDto
     * @return result
     */
    @PostMapping("/getMyMeetingSignInfo")
    public TableDataInfo getMyMeetingSignPage(@RequestBody MeetingSignPageDto pageDto) {
        IPage<MeetingSignPageVo> resultPage = meetingSignService.getMyMeetingSignPage(pageDto);
        return getDataTable(resultPage.getRecords(), resultPage.getTotal());
    }

    /**
     * 委员签到
     * @param signDetailId signDetailId
     * @return result
     */
    @Secured("portal")
    @PostMapping("/memberSignIn")
    public AjaxResult memberSignIn(@RequestParam String signDetailId) {
        return success(meetingSignDetailService.signIn(signDetailId));
    }

    /**
     * 获取会议签到详情情况
     * @param signId signId
     * @return result
     */
    @GetMapping("/getSignInfoDetail")
    public TableDataInfo getSignInfoDetailDetail(@RequestParam(value = "signId") String signId) {
        return getDataTable(meetingSignDetailService.getMeetingSignDetailList(signId));
    }

    // --------------------------Meeting Document--------------------------
    /**
     * 上传会议资料
     * @param createDto createDto
     * @return result
     */
    @PostMapping("/doc/create")
    public AjaxResult addMeetingDoc(@Valid @RequestBody MeetingDocEditDto createDto) {
        return success(meetingDocService.saveMeetingDoc(createDto));
    }

    /**
     * 修改会议资料
     * @param editDto editDto
     * @return result
     */
    @PutMapping("/doc/edit")
    public AjaxResult editMeetingDoc(@Valid @RequestBody MeetingDocEditDto editDto) {
        return success(meetingDocService.updateMeetingDoc(editDto));
    }

    /**
     * 删除会议资料
     * @param id id
     * @return result
     */
    @DeleteMapping("/doc/remove")
    public AjaxResult removeMeetingDoc(@RequestParam String id) {
        return toAjax(meetingDocService.deleteMeetingDoc(id));
    }

    /**
     * 获取会议资料信息
     * @param pageDto pageDto
     * @return result
     */
    @PostMapping("/doc/page")
    public TableDataInfo getMeetingDocPage(@RequestBody MeetingDocPageDto pageDto) {
        IPage<MeetingDocPageVo> resultPage = meetingDocService.getMeetingDocPage(pageDto);
        return getDataTable(resultPage.getRecords(), resultPage.getTotal());
    }

    /**
     * 获取会议资料详情
     * @param docId docId
     * @return result
     */
    @GetMapping("/doc/get")
    public AjaxResult getMeetingDocInfo(@RequestParam String docId) {
        return AjaxResult.success(meetingDocService.getMeetingDoc(docId));
    }
}
