package com.ruoyi.project.proposal.service.impl;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.security.LoginUser;
import com.ruoyi.project.proposal.converter.IssueFeedbackConverter;
import com.ruoyi.project.proposal.domain.IssueFeedback;
import com.ruoyi.project.proposal.domain.vo.IssueFeedbackVo;
import com.ruoyi.project.proposal.mapper.IssueFeedbackMapper;
import com.ruoyi.project.proposal.service.IIssueFeedbackService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class IssueFeedbackServiceImpl implements IIssueFeedbackService {

    @Resource
    private IssueFeedbackMapper issueFeedbackMapper;

    @Override
    public List<IssueFeedbackVo> getFeedbackList() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<IssueFeedback> issueFeedbackList = issueFeedbackMapper.getFeedbackList(loginUser.getUsername());
        return IssueFeedbackConverter.INSTANCE.convert(issueFeedbackList);
    }

    @Override
    public Integer insertFeedback(IssueFeedback issueFeedback) {
        return issueFeedbackMapper.insert(issueFeedback);
    }

    @Override
    public Integer updateFeedback(IssueFeedback issueFeedback) {
        return issueFeedbackMapper.updateById(issueFeedback);
    }
}
