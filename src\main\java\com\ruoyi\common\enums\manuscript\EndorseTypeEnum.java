package com.ruoyi.common.enums.manuscript;

import lombok.Getter;

@Getter
public enum EndorseTypeEnum {

    /**
     * 区级领导批示
     */
    DISTRICT("DISTRICT", "区领导批示"),

    /**
     * 市级领导批示
     */
    CITY("CITY", "市领导批示"),

    /**
     * 省级领导批示
     */
    PROVINCE("PROVINCE", "省领导批示");

    private final String code;
    private final String description;

    EndorseTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     * @param code 枚举编码
     * @return 对应的枚举对象，未找到返回null
     */
    public static EndorseTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (EndorseTypeEnum type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据description获取枚举
     * @param description 枚举描述
     * @return 对应的枚举对象，未找到返回null
     */
    public static EndorseTypeEnum getByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (EndorseTypeEnum type : values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        return null;
    }

}
