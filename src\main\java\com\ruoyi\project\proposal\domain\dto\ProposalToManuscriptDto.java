package com.ruoyi.project.proposal.domain.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.proposal.SubmitTypeEnum;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.proposal.domain.vo.ProposalUserRelVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProposalToManuscriptDto {


    /** 提案年度 */
    private Long year;


    /** 提案类别(经济,政治,文化,社会,生态文明,其他) */
    private String caseType;

    /** 提案案别 */
    private String caseCategory;

    /** 提案案由 */
    private String caseReason;

    /** 提案内容 */
    private String caseContent;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerDate;

    /** 提交类型(个人，个人联名，集体) */
    private SubmitTypeEnum submitType;

    /** 是否公开 */
    private Boolean isOpen;

    /** 创建人 */
    private String createBy;

    /** 备注 */
    private String remark;

    /***
     * 附件列表
     */
    private List<AnnexVo> annexIdList;

    /**
     * 提案者列表
     */
    private List<ProposalUserRelVo> proposerList;

}
