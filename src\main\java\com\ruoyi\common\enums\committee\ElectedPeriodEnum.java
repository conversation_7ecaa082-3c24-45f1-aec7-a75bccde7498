package com.ruoyi.common.enums.committee;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 当选届期枚举
 */
@Getter
public enum ElectedPeriodEnum {

    FIRST("一届", "1"),
    SECOND("二届", "2"),
    THIRD("三届", "3"),
    FOURTH("四届", "4"),
    FIFTH("五届", "5"),
    SIXTH("六届", "6"),
    SEVENTH("七届", "7"),
    EIGHTH("八届", "8");

    private final String chineseName;
    private final String codePrefix;

    // 静态映射，用于快速查找
    private static final Map<String, String> CHINESE_TO_CODE_MAP = new HashMap<>();

    static {
        for (ElectedPeriodEnum period : ElectedPeriodEnum.values()) {
            CHINESE_TO_CODE_MAP.put(period.getChineseName(), period.getCodePrefix());
        }
    }

    ElectedPeriodEnum(String chineseName, String codePrefix) {
        this.chineseName = chineseName;
        this.codePrefix = codePrefix;
    }

    /**
     * 根据中文名称获取对应的代码前缀
     * @param chineseName 中文名称，如"一届"
     * @return 代码前缀，如"1"，如果没有匹配则返回原值
     */
    public static String getCodePrefixByChineseName(String chineseName) {
        return CHINESE_TO_CODE_MAP.getOrDefault(chineseName, chineseName);
    }
}
