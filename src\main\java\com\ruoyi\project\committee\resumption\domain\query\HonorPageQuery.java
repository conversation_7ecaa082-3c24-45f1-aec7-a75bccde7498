package com.ruoyi.project.committee.resumption.domain.query;

import com.ruoyi.common.domain.query.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class HonorPageQuery extends PageQuery {

    private Integer currentPage = 1;

    @ApiModelProperty("届")
    private String electedPeriod;

    @ApiModelProperty("次")
    private String electedTimes;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ApiModelProperty("专委会")
    private String committee;

    @ApiModelProperty("参与人")
    private String participantsName;

    @ApiModelProperty("填报开始时间")
    private Date startTime;

    @ApiModelProperty("填报结束时间")
    private Date endTime;
} 