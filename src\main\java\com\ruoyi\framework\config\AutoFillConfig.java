package com.ruoyi.framework.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.security.LoginUser;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

@Component
public class AutoFillConfig implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        LoginUser loginUser = SecurityUtils.getLoginUser();

        this.setFieldValByName("createBy", loginUser.getUsername(), metaObject);
        this.setFieldValByName("createTime", new Date(System.currentTimeMillis()), metaObject);
        this.setFieldValByName("updateBy", loginUser.getUsername(), metaObject);
        this.setFieldValByName("updateTime",  new Date(System.currentTimeMillis()), metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        LoginUser loginUser = SecurityUtils.getLoginUser();

        this.setFieldValByName("updateBy", loginUser.getUsername(), metaObject);
        this.setFieldValByName("updateTime", new Date(System.currentTimeMillis()), metaObject);
    }

}
