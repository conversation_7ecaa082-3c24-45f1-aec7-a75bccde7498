package com.ruoyi.project.proposal.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.proposal.domain.ProposalReception;
import com.ruoyi.project.proposal.domain.vo.ProposalReceptionVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 提案接受情况信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Mapper
public interface ProposalReceptionMapper extends BaseMapper<ProposalReception> {

    /**
     * 查询提案接受情况信息列表
     * 
     * @param proposalId 提案id
     * @return 提案接受情况信息集合
     */
    public List<ProposalReceptionVo> selectProposalReceptionList(String proposalId);

    /**
     * 查询提案接收情况信息列表
     * @param idList idList
     * @return result
     */
    public List<ProposalReceptionVo> selectReceptionByProposalIds(List<String> idList);


    /**
     * 批量删除提案接受情况信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProposalReceptionByIds(Long[] ids);

    default boolean existUserReception(String proposalId, Long userId) {
        return exists(new LambdaQueryWrapper<ProposalReception>()
                .eq(ProposalReception::getProposalId, proposalId)
                .eq(ProposalReception::getRecipientId, userId)
        );
    };

    default boolean checkReceived(String proposalId, Long userId) {
        return exists(new LambdaQueryWrapper<ProposalReception>()
                .eq(ProposalReception::getProposalId, proposalId)
                .eq(ProposalReception::getRecipientId, userId)
                .eq(ProposalReception::getReceiveStatus, true)
        );
    }

    default void receive(String proposalId, Long userId) {
        update(null, new LambdaUpdateWrapper<ProposalReception>()
                .set(ProposalReception::getReceiveTime, new Date())
                .set(ProposalReception::getReceiveStatus, true)
                .eq(ProposalReception::getProposalId, proposalId)
                .eq(ProposalReception::getRecipientId, userId));
    }
}
