package com.ruoyi.project.proposal.service.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.security.LoginUser;
import com.ruoyi.project.proposal.domain.ProposalSupervisionRecord;
import com.ruoyi.project.proposal.mapper.ProposalSupervisionRecordMapper;
import com.ruoyi.project.proposal.service.IProposalSupervisionRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ProposalSupervisionRecordServiceImpl implements IProposalSupervisionRecordService {

    @Resource
    private ProposalSupervisionRecordMapper supervisionRecordMapper;

    @Override
    public List<ProposalSupervisionRecord> getSupervisionRecords(String proposalId) {
        return supervisionRecordMapper.getSupervisionRecords(proposalId);
    }

    @Override
    public Integer addSupervisionRecord(String proposalId) {

        ProposalSupervisionRecord record = new ProposalSupervisionRecord();

        LoginUser loginUser = SecurityUtils.getLoginUser();
        record.setProposalId(proposalId);
        record.setContact(loginUser.getUsername());
        record.setContactPhone(loginUser.getUser().getPhonenumber());
        record.setCreateTime(DateUtil.date());

        return supervisionRecordMapper.insert(record);
    }
}
