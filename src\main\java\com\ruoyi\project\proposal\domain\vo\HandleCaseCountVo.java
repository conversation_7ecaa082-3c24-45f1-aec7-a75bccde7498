package com.ruoyi.project.proposal.domain.vo;


import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class HandleCaseCountVo {

    private Integer no;

    private String organizer;

    // 单办
    private Long singleOffice = 0L;

    // 分办
    private Long distributedWork = 0L;

    // 主办
    private Long leadOffice = 0L;

    // 协办
    private Long assistantOffice = 0L;

    // 合计
    private Long total = 0L;

    public HandleCaseCountVo(String organizer) {
        this.organizer = organizer;
    }
}
