package com.ruoyi.project.community.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("manuscript_content")
public class ManuscriptContent {

    @TableId
    private String id;

    /** 稿件id */
    private String manuscriptId;

    /** 原文内容 */
    private String originContent;

    /** 最新内容 */
    private String latestContent;

    /** 创建者 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新者 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 逻辑删除标志位 */
    @TableLogic
    private Boolean delFlag;

}
