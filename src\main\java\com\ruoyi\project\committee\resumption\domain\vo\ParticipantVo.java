package com.ruoyi.project.committee.resumption.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.enums.committee.PersonTypeEnum;
import com.ruoyi.common.enums.committee.ReportTypeEnum;
import lombok.Data;

@Data
public class ParticipantVo {

    @JsonIgnore
    private String pkId;

    private String committeePkId;

    private String committeeName;

    private String unitPost;

    @JsonIgnore
    private ReportTypeEnum reportType;

    @JsonIgnore
    private PersonTypeEnum personType;
}
