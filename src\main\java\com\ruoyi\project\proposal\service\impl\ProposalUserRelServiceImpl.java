package com.ruoyi.project.proposal.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.proposal.domain.vo.ProposalUserRelVo;
import com.ruoyi.project.proposal.domain.vo.ProposerVo;
import org.springframework.stereotype.Service;
import com.ruoyi.project.proposal.mapper.ProposalUserRelMapper;
import com.ruoyi.project.proposal.domain.ProposalUserRel;
import com.ruoyi.project.proposal.service.IProposalUserRelService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 提案人员关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Service
public class ProposalUserRelServiceImpl extends ServiceImpl<ProposalUserRelMapper, ProposalUserRel> implements IProposalUserRelService {

    @Resource
    private ProposalUserRelMapper proposalUserRelMapper;

    @Override
    public List<ProposalUserRelVo> selectProposalUserListById(String proposalId) {
        return proposalUserRelMapper.getProposalUserById(proposalId);
    }

    @Override
    public Set<String> selectByProposerId(String proposerId) {
        List<ProposalUserRel> relList = proposalUserRelMapper.selectByProposerId(proposerId);
        if (ObjectUtil.isNotEmpty(relList)) {
            return relList.stream().map(ProposalUserRel::getProposalId).collect(Collectors.toSet());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProposalUserRel(String proposalId, List<ProposerVo> proposerList) {
        if (ObjectUtil.isNotEmpty(proposerList)) {
            // 删除关联关系
            remove(new LambdaQueryWrapper<ProposalUserRel>().eq(ProposalUserRel::getProposalId, proposalId));

            List<ProposalUserRel> proposalUserRelList = new ArrayList<>();
            for (ProposerVo proposerVo : proposerList) {
                ProposalUserRel proposalUserRel = new ProposalUserRel();
                proposalUserRel.setProposalId(proposalId);
                proposalUserRel.setProposerId(proposerVo.getProposerId());
                proposalUserRel.setSubmitType(proposerVo.getSubmitType());
                proposalUserRelList.add(proposalUserRel);
            }
            saveBatch(proposalUserRelList);
        }

    }
}
