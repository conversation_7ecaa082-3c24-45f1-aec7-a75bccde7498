package com.ruoyi.project.committee.meeting.domain.dto;

import com.ruoyi.common.enums.committee.DocTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MeetingDocEditDto {

    private String id;

    private String meetingId;

    @ApiModelProperty(value = "文件标题")
    private String title;

    @ApiModelProperty(value = "文件内容")
    private String content;

    @ApiModelProperty(value = "文件类型")
    private DocTypeEnum docType;

    @ApiModelProperty(value = "是否公开")
    private Boolean isPublicly;

    @ApiModelProperty(value = "发言人")
    private String speaker;

    @ApiModelProperty(value = "附件列表")
    private List<String> annexList;
}
