package com.ruoyi.project.activity.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 活动基本信息分页查询DTO
 */
@Data
public class ActivityBasicinfoPageDTO {

    /**
     * 当前页码
     */
    private Integer currentPage = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;

    /**
     * 活动主题
     */
    private String title;

    /**
     * 活动类型
     */
    private String type;

    /**
     * 活动城市
     */
    private String activityCity;

    /**
     * 活动状态
     */
    private String status;

    /**
     * 补录状态
     */
    private String supplementStatus;

    /**
     * 活动开始时间范围（开始）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 活动开始时间范围（结束）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 发起部门名称（模糊查询）
     */
    private String deptName;
}
