package com.ruoyi.project.activity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 活动签到实体类
 */
@Data
//@EqualsAndHashCode(callSuper = true)
@TableName("activity_sign")
public class ActivitySign{
    
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 原UUID主键
     */
    @TableField("PKID")
    private String pkid;
    
    /**
     * 活动ID
     */
    @TableField("Activity_PKID")
    private String activityPkid;
    
    /**
     * 人员ID
     */
    @TableField("Pepole_PKID")
    private String pepolePkid;
    
    /**
     * 签到开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Sign_BeginDate")
    private Date signBeginDate;
    
    /**
     * 签到结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("Sign_EndDate")
    private Date signEndDate;
    
    /**
     * 原因说明
     */
    @TableField("Reason")
    private String reason;
    
    /**
     * 人员姓名
     */
    @TableField("Pepole_name")
    private String pepoleName;
    
    /**
     * 区域ID
     */
    @TableField("region_id")
    private String regionId;
} 