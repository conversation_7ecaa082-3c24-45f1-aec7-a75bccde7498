package com.ruoyi.project.proposal.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.utils.file.FileTypeUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.proposal.domain.Annex;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import com.ruoyi.project.proposal.service.IAnnexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/annex")
public class AnnexController {

    @Value(value = "${ruoyi.uploadPath}")
    private String uploadPath;

    @Autowired
    private ServerConfig serverConfig;

    @Resource
    private IAnnexService annexService;

    @GetMapping("/getProposalAnnex")
    public AjaxResult getProposalAnnex(@RequestParam("id") Long proposalId) {
        return AjaxResult.success(annexService.selectAnnexByProposalId(proposalId));
    }


    @PostMapping("/upload")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadAnnex(@RequestParam("file") MultipartFile file, @RequestParam(value = "filename", required = false) String filename) {
        AnnexVo annexVo = null;
        try {

                String filePath = FileUploadUtils.upload(file);

                Annex annex = new Annex();
                String annexName = ObjectUtil.isEmpty(filename) ? file.getOriginalFilename() : filename;
                annex.setAnnexName(annexName);
                annex.setUrl(uploadPath + filePath);
                annex.setAnnexType(FileTypeUtils.getFileType(Objects.requireNonNull(file.getOriginalFilename())));

                annexService.insertAnnex(annex);
                annexVo = BeanUtil.copyProperties(annex, AnnexVo.class);

        } catch (IOException e) {
            log.info("上传附件失败", e);
            return AjaxResult.error("上传附件失败！");
        }
        return AjaxResult.success(annexVo);
    }

}
