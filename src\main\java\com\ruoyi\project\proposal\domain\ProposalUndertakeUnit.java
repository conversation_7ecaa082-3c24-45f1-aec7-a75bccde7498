package com.ruoyi.project.proposal.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.enums.proposal.HandleWayEnum;
import com.ruoyi.common.enums.proposal.UndertakeResultEnum;
import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import lombok.Data;

import java.util.Date;

@Data
@TableName("proposal_undertake_unit")
public class ProposalUndertakeUnit {

    private Long id;

    /** 办理id */
    private Long handleId;

    /** 承办单位id */
    private Long unitId;

    /** 承办方式 */
    private UndertakeWayEnum undertakeWay;

    /** 承办结果 */
    private UndertakeResultEnum undertakeResult;

    /** 办理方式 */
    private HandleWayEnum handleWay;

    /** 办理截止日期 */
    private Date deadline;

    /** 办理状态 */
    private Boolean handleStatus;

    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableLogic
    private Boolean delFlag;

}
