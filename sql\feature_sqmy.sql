-- manuscript definition
CREATE TABLE `manuscript` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code` varchar(100) DEFAULT NULL COMMENT '编号',
    `year` int DEFAULT NULL COMMENT '年度',
    `title` text COMMENT '标题',
    `content` longtext COMMENT '正文',
    `category` varchar(25) DEFAULT NULL COMMENT '稿件类别',
    `category_detail` varchar(50) DEFAULT NULL COMMENT '类别明细',
    `reflect_unit` varchar(255) DEFAULT NULL COMMENT '反映单位',
    `reflector` varchar(255) DEFAULT NULL COMMENT '反映人',
    `contact` varchar(100) DEFAULT NULL COMMENT '联系人',
    `status` varchar(25) DEFAULT NULL COMMENT '稿件状态',
    `issue_number` varchar(255) DEFAULT NULL COMMENT '签发期数',
    `source` varchar(25) DEFAULT NULL COMMENT '信息来源',
    `adopt_way` varchar(25) DEFAULT NULL COMMENT '采用方式',
    `submit_way` varchar(255) DEFAULT NULL COMMENT '来稿方式',
    `recipient` varchar(255) DEFAULT NULL COMMENT '主送单位',
    `cc_unit` varchar(255) DEFAULT NULL COMMENT '抄送单位',
    `report_type` varchar(25) DEFAULT NULL COMMENT '报送类别',
    `report_unit` varchar(255) DEFAULT NULL COMMENT '报送单位',
    `carbon_copy` varchar(255) DEFAULT NULL COMMENT '抄送',
    `is_feedback` tinyint(1) DEFAULT '0' COMMENT '是否反馈',
    `is_endorsed` tinyint(1) DEFAULT '0' COMMENT '是否批示',
    `submission_time` datetime DEFAULT NULL COMMENT '来稿时间',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='社情民意信息表';

ALTER TABLE manuscript ADD is_publish BOOL NULL COMMENT '是否公开';
ALTER TABLE manuscript CHANGE is_publish is_publish BOOL NULL COMMENT '是否公开' AFTER carbon_copy;

ALTER TABLE manuscript ADD report_target varchar(100) NULL COMMENT '报送对象';
ALTER TABLE manuscript CHANGE report_target report_target varchar(100) NULL COMMENT '报送对象' AFTER report_type;

ALTER TABLE manuscript ADD other_report_target varchar(255) NULL COMMENT '其他报送对象';
ALTER TABLE manuscript CHANGE other_report_target other_report_target varchar(255) NULL COMMENT '其他报送对象' AFTER report_target;

ALTER TABLE manuscript ADD other_report_target varchar(255) NULL COMMENT '其他报送对象';
ALTER TABLE manuscript CHANGE other_report_target other_report_target varchar(255) NULL COMMENT '其他报送对象' AFTER report_target;

ALTER TABLE manuscript ADD report_unit_detail TEXT NULL COMMENT '报送单位详情';
ALTER TABLE manuscript CHANGE report_unit_detail report_unit_detail TEXT NULL AFTER report_unit;



-- manuscript_annex_rel definition
CREATE TABLE `manuscript_annex_rel` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `manuscript_id` bigint DEFAULT NULL COMMENT '社情民意id',
    `annex_id` bigint DEFAULT NULL COMMENT '附件id',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='社情民意附件关联表';



-- manuscript_reflector_rel definition
CREATE TABLE `manuscript_reflector_rel` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `manuscript_id` bigint DEFAULT NULL COMMENT '稿件id',
    `user_id` bigint DEFAULT NULL COMMENT '反映人id',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='社情民意反映人关联表';


-- manuscript_endorsement definition
CREATE TABLE `manuscript_endorsement` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `manuscript_id` bigint DEFAULT NULL COMMENT '稿件id',
    `endorsement` longtext COMMENT '批示信息',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='社情民意稿件批示信息';


-- manuscript_feedback definition
CREATE TABLE `manuscript_feedback` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `manuscript_id` bigint DEFAULT NULL COMMENT '稿件id',
    `feedback` longtext COMMENT '反馈信息',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='社情民意稿件反馈信息';


-- `ry-vue`.manuscript_record definition

CREATE TABLE `manuscript_record` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `manuscript_id` bigint DEFAULT NULL COMMENT '稿件id',
    `audit_phase` varchar(100) DEFAULT NULL COMMENT '审核环节',
    `audit_opinion` text COMMENT '审核意见',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='稿件审核记录';


-- manuscript_content definition
CREATE TABLE `manuscript_content` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `manuscript_id` bigint DEFAULT NULL COMMENT '稿件id',
    `origin_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '原始稿件',
    `latest_content` longtext COMMENT '最新正文内容',
    `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志位',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='稿件正文信息表';

