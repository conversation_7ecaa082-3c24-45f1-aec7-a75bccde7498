package com.ruoyi.project.committee.meeting.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignEditDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingSignPageDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingSignPageVo;


/**
 * 会议签到信息 Service接口
 */
public interface IMeetingSignService {

    IPage<MeetingSignPageVo> getMeetingSignPage(MeetingSignPageDto pageDto);

    IPage<MeetingSignPageVo> getMyMeetingSignPage(MeetingSignPageDto pageDto);

    Long createMeetingSign(MeetingSignEditDto createDto);

}
