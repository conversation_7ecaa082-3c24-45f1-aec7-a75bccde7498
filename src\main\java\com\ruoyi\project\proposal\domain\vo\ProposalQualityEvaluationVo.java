package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 质量评价信息对象 proposal_quality_evaluation
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
public class ProposalQualityEvaluationVo{

    /** id */
    private String id;

    /** 单位名称 */
    private String deptName;

    /** 总体评价 */
    private String evaluation;

    /** 评价时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date evaluationTime;


}
