package com.ruoyi.project.committee.resumption.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.committee.resumption.domain.po.HonorAnnex;
import com.ruoyi.project.committee.resumption.mapper.HonorAnnexMapper;
import com.ruoyi.project.committee.resumption.service.IHonorAnnexService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 荣誉附件Service实现
 */
@Service
@RequiredArgsConstructor
public class HonorAnnexServiceImpl extends ServiceImpl<HonorAnnexMapper, HonorAnnex> implements IHonorAnnexService {

    /**
     * 根据荣誉ID查询附件列表
     *
     * @param honorId 荣誉ID
     * @return 附件列表
     */
    @Override
    public List<HonorAnnex> selectListByHonorId(String honorId) {
        return getBaseMapper().selectListByHonorId(honorId);
    }

    /**
     * 根据荣誉ID删除附件
     *
     * @param honorId 荣誉ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByHonorId(String honorId) {
        getBaseMapper().deleteByHonorId(honorId);
    }

    /**
     * 批量保存附件
     *
     * @param annexList 附件列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<HonorAnnex> annexList) {
        return super.saveBatch(annexList);
    }
}
