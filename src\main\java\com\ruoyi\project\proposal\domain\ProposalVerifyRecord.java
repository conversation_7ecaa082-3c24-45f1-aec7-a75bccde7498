package com.ruoyi.project.proposal.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 提案审核记录信息对象 proposal_verify_record
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProposalVerifyRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /** 提案id */
    @Excel(name = "提案id")
    private String proposalId;

    @Excel(name = "审核类型")
    private String verifyType;

    /** 审核环节 */
    @Excel(name = "审核环节")
    private String verifyProcess;

    @Excel(name = "审核原因")
    private String verifyReason;

    /** 审核时间 */
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date verifyTime;

    /** 审核日志 */
    @Excel(name = "审核日志")
    private String verifyLog;

    /** 采取措施 */
    private String measure;

    /** 相关部门 */
    private String relateUnit;

    /** 审核人id */
    @Excel(name = "审核人")
    private String verifier;

    /** 删除标识位 */
    private Boolean delFlag;

}
