package com.ruoyi.project.committee.resumption.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 荣誉详情视图对象
 */
@Data
public class HonorDetailVo {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("获奖时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty("填报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("获奖类型code")
    private String honorType;

    @ApiModelProperty("获奖类型")
    private String honorTypeName;

    @ApiModelProperty("获奖等级")
    private String honorLevel;

    @ApiModelProperty("区县级")
    private Integer districtLevel;

    @ApiModelProperty("市级")
    private Integer cityLevel;

    @ApiModelProperty("省级")
    private Integer provinceLevel;

    @ApiModelProperty("国家级")
    private Integer nationalLevel;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("参与人")
    private String participantsName;

    @ApiModelProperty("附件列表")
    private List<HonorAnnexVo> annexList;

    @ApiModelProperty("审核状态code")
    private String auditStatus;

    @ApiModelProperty("审核状态")
    private String auditStatusName;

    @ApiModelProperty("审核理由")
    private String checkReason;

    @ApiModelProperty("审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @ApiModelProperty("参与人")
    private List<ParticipantVo> participantList;
}
