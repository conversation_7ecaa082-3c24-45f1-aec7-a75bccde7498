package com.ruoyi.project.proposal.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

@Data
public class ProposalReceptionVo {


    private String id;

    private String proposalId;

    private String userId;

    private String deptName;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date joinTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date receiveTime;

    private Boolean receiveStatus;

    private String undertakeWay;

}
