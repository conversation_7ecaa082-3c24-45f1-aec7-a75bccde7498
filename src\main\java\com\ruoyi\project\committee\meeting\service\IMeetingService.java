package com.ruoyi.project.committee.meeting.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingEditDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingPageDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingInfoPageVo;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingInfoVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 会议管理Service接口
 */
public interface IMeetingService {

    /**
     * 获取会议分页列表
     *
     * @param pageDto 分页查询参数
     * @return 会议信息分页数据
     */
    IPage<MeetingInfoPageVo> getMeetingPage(MeetingPageDto pageDto);

    /**
     * 获取我的会议分页列表
     *
     * @param pageDto 分页查询参数
     * @return 会议信息分页数据
     */
    IPage<MeetingInfoPageVo> getMyPage(MeetingPageDto pageDto);

    /**
     * 获取会议详细信息
     *
     * @param id 会议ID
     * @return 会议信息
     */
    MeetingInfoVo getMeetingById(String id);

    /**
     * 新增会议信息
     *
     * @param meetingEditDto 会议信息
     * @return 结果
     */
    Long createMeetingInfo(MeetingEditDto meetingEditDto);

    /**
     * 修改会议信息
     *
     * @param meetingEditDto 会议信息
     * @return 结果
     */
    Boolean updateMeetingInfo(MeetingEditDto meetingEditDto);

    /**
     * 删除会议信息
     *
     * @param id 会议ID
     * @return 结果
     */
    Boolean deleteMeetingInfo(String id);

    /**
     * 发布会议信息
     * @param idList idList
     * @return result
     */
    Boolean publish(List<String> idList);

    /**
     * 取消发布会议信息
     * @param idList idList
     * @return result
     */
    Boolean unPublish(List<String> idList);

    /**
     * 导出参加会议人员
     *
     * @param id 会议ID
     * @param response 响应
     */
    void exportParticipants(String id, HttpServletResponse response);
}
