package com.ruoyi.project.committee.meeting.domain.vo;

import com.ruoyi.common.enums.committee.DocTypeEnum;
import com.ruoyi.project.proposal.domain.vo.AnnexVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MeetingDocVo {

    private String id;

    @ApiModelProperty(value = "文件标题")
    private String title;

    @ApiModelProperty(value = "文件内容")
    private String content;

    @ApiModelProperty(value = "文件类型")
    private String docType;

    @ApiModelProperty(value = "是否公开")
    private Boolean isPublicly;

    @ApiModelProperty(value = "发言人ID")
    private String speakerId;

    @ApiModelProperty(value = "发言人")
    private String speaker;

    @ApiModelProperty(value = "附件列表")
    private List<AnnexVo> annexList;
}
