<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.committee.meeting.mapper.MeetingSignMapper">

    <select id="getMyMeetingSignPage"
            resultType="com.ruoyi.project.committee.meeting.domain.vo.MeetingSignPageVo">
        SELECT
            ms.id AS id,
            sd.id AS detailId,
            m.meeting_name AS meetingName,
            ms.reason AS reason,
            ms.sign_begin_time AS signBeginTime,
            ms.sign_end_time AS signEndTime,
            sd.is_sign AS isSign,
            sd.is_leave AS isLeave
        FROM meeting_info m
            LEFT JOIN meeting_sign ms ON ms.meeting_id = m.id AND ms.del_flag = false
            LEFT JOIN meeting_sign_detail sd ON sd.sign_id = ms.id AND sd.del_flag = false
            LEFT JOIN sys_committee_member cm ON cm.id = sd.people_id
        WHERE m.del_flag = false
            AND m.id = #{pageDto.meetingId}
            AND cm.user_id = #{currentUserId}
        <if test="pageDto.reason != null and pageDto.reason != ''">
            AND ms.reason LIKE CONCAT('%',#{pageDto.meetingName},'%')
        </if>
    </select>
</mapper>