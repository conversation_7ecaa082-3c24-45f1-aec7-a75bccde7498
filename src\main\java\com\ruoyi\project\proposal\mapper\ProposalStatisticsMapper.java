package com.ruoyi.project.proposal.mapper;

import com.ruoyi.project.proposal.domain.ProposalHandle;
import com.ruoyi.project.proposal.domain.dto.ProposalStatisticsDto;
import com.ruoyi.project.proposal.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProposalStatisticsMapper {

    List<ProposalHandle> selectResultStatistics(@Param("year") Integer statisticalYear);

    List<ProposalUnitStatisticVo> analyzeDeptProposalCount(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<UndertakeUnitVo> analyzeByGovOrPartyDept(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<HandleProgressStatsVo> analyzeHandleProgress(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<ProposalReceptionStatisticsVo> analyzeProposalReception(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<HandleStatisticsVo> analyzeHandleCount(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<String> selectFinishProposalUnit(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<String> selectProcessingProposalUnit(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<String> selectNotStartedProposalUnit(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<ProposalHandle> selectProposalHandle(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<HandleStatisticsVo> selectHandleProgress(@Param("statisticsDto") ProposalStatisticsDto statisticsDto);

    List<ProposalUnitStatisticVo> selectJointOffice(@Param("year") Integer statisticalYear);

    List<ProposalUnitStatisticVo> selectLeadOffice(@Param("year") Integer statisticalYear);

    // 新增方法：正确区分已报/未报状态
    List<ProposalUnitStatisticVo> selectJointOfficeReported(@Param("year") Integer year);

    List<ProposalUnitStatisticVo> selectJointOfficeUnReported(@Param("year") Integer year);

    List<ProposalUnitStatisticVo> selectLeadOfficeReported(@Param("year") Integer year);

    List<ProposalUnitStatisticVo> selectLeadOfficeUnReported(@Param("year") Integer year);

    List<MemberProposalStatisticsVo> analyzeMemberProposal(@Param("year") Integer year);

    List<MemberProposalDetailStatisticsVo> analyzeMemberProposalDetail(@Param("year") Integer year);
}
