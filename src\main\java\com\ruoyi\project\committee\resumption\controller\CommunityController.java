package com.ruoyi.project.committee.resumption.controller;


import com.ruoyi.common.domain.dto.PageDTO;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityAddDTO;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityAuditDTO;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityUpdateDTO;
import com.ruoyi.project.committee.resumption.domain.query.CommunityPageQuery;
import com.ruoyi.project.committee.resumption.domain.query.CommunityUserPageQuery;
import com.ruoyi.project.committee.resumption.domain.vo.CommunityDetailVo;
import com.ruoyi.project.committee.resumption.domain.vo.CommunityPageVo;
import com.ruoyi.project.committee.resumption.service.ICommunityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/**
 * 公益情况Controller
 */
@Api(tags = "公益情况管理")
@RestController
@RequestMapping("/community")
public class CommunityController extends BaseController {

    @Autowired
    private ICommunityService communityService;

    @ApiOperation("获取公益情况列表")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody CommunityPageQuery query) {
        PageDTO<CommunityPageVo> pageDTO = communityService.getCommunityList(query);
        return getDataTable(pageDTO.getList(), pageDTO.getTotal());
    }

    @ApiOperation("获取公益情况详情")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(communityService.getCommunityDetail(id));
    }

    @ApiOperation("新增公益情况")
    @Log(title = "新增公益情况", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addCommunity(@RequestBody CommunityAddDTO communityAddDTO) {
        return communityService.addCommunity(communityAddDTO);
    }

    @ApiOperation("修改公益情况")
    @Log(title = "修改公益情况", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult updateCommunity(@RequestBody CommunityUpdateDTO communityUpdateDTO) {
        return communityService.updateCommunity(communityUpdateDTO);
    }

    @ApiOperation("审核公益情况")
    @Log(title = "审核公益情况", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult auditCommunity(@RequestBody CommunityAuditDTO communityAuditDTO) {
        return communityService.auditCommunity(communityAuditDTO);
    }

    @ApiOperation("删除公益情况")
    @Log(title = "删除公益情况", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult deleteCommunity(@RequestBody List<String> ids) {
        return communityService.deleteCommunity(ids);
    }

    @ApiOperation("退回公益情况")
    @Log(title = "退回公益情况", businessType = BusinessType.UPDATE)
    @PostMapping("/return")
    public AjaxResult returnCommunity(@RequestBody CommunityAuditDTO communityAuditDTO) {
        return communityService.returnCommunity(communityAuditDTO);
    }

    @ApiOperation("查询当前用户的公益情况")
    @PostMapping("/getCurrentUserList")
    public TableDataInfo getCurrentUserList(@RequestBody CommunityUserPageQuery query) {
        PageDTO<CommunityPageVo> pageDTO = communityService.getCurrentUserCommunityList(query);
        return getDataTable(pageDTO.getList(), pageDTO.getTotal());
    }
}
