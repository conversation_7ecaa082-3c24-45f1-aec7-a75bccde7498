package com.ruoyi.project.committee.meeting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.project.committee.meeting.domain.MeetingParticipants;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingAttendInfoDto;
import com.ruoyi.project.committee.meeting.domain.dto.MeetingParticipantSearchDto;
import com.ruoyi.project.committee.meeting.domain.vo.MeetingParticipantsVo;
import com.ruoyi.project.committee.meeting.mapper.MeetingParticipantsMapper;
import com.ruoyi.project.committee.meeting.service.IMeetingParticipantsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会议参与委员信息 Service业务层处理
 */
@Service
public class MeetingParticipantsServiceImpl extends ServiceImpl<MeetingParticipantsMapper, MeetingParticipants> implements IMeetingParticipantsService {

    @Resource
    private MeetingParticipantsMapper meetingParticipantsMapper;

    @Override
    public List<MeetingParticipantsVo> getMeetingParticipantsList(MeetingParticipantSearchDto searchDto) {
        return meetingParticipantsMapper.getMeetingParticipantsList(searchDto);
    }

    @Override
    public String getMeetingParticipants(String meetingId) {
        String meetingParticipants = null;

        List<MeetingParticipantsVo> participantsList = meetingParticipantsMapper.getParticipantsListByMeetingId(Long.valueOf(meetingId));
        if (ObjectUtil.isNotEmpty(participantsList)) {
            meetingParticipants = participantsList.stream().map(MeetingParticipantsVo::getPeopleName).collect(Collectors.joining(","));
        }

        return meetingParticipants;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createParticipants(Long meetingId, List<String> peopleIdList) {

        if (ObjectUtil.isEmpty(peopleIdList)) {
            return;
        }

        // 批量创建新的参与委员信息
        List<MeetingParticipants> participantsList = new ArrayList<>();

        for (String peopleId : peopleIdList) {
            MeetingParticipants participants = new MeetingParticipants();
            participants.setMeetingId(meetingId);
            participants.setPeopleId(Long.valueOf(peopleId));
            participants.setIsAttend(false);
            participantsList.add(participants);
        }

        // 批量保存
        this.saveBatch(participantsList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetParticipants(Long meetingId, List<String> peopleIdList) {
        meetingParticipantsMapper.deleteBtMeetingId(meetingId);
        if (ObjectUtil.isNotEmpty(peopleIdList)) {
            createParticipants(meetingId, peopleIdList);
        }
    }

    @Override
    public Boolean saveAttendInfo(MeetingAttendInfoDto attendDto) {
        if (meetingParticipantsMapper.selectById(attendDto.getParticipantId()) == null) {
            throw new ServiceException("您不在该会议的参与名单中，无法操作");
        }
        Integer affectRows = meetingParticipantsMapper.saveAttendInfo(attendDto);
        return affectRows > 0;
    }
}