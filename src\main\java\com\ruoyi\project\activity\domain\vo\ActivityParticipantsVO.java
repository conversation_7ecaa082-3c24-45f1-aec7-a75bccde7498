package com.ruoyi.project.activity.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 活动参与人员VO
 */
@Data
public class ActivityParticipantsVO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 原UUID主键
     */
    private String pkid;

    /**
     * 活动ID
     */
    private String activityPkid;

    /**
     * 人员ID
     */
    private String pepolePkid;

    /**
     * 是否参加(1:参加 0:不参加 2:请假)
     */
    private String isAttend;

    /**
     * 参加状态文本
     */
    @ApiModelProperty(value = "参加状态文本")
    private String attendStatusText;

    /**
     * 不参加/请假原因
     */
    private String noAttendReason;

    /**
     * 创建人ID
     */
    private String createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人ID
     */
    private String updateId;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 是否负责人(1:是 0:否)
     */
    private String isLeader;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 区域ID
     */
    private String regionId;

    /**
     * 人员姓名
     */
    @ApiModelProperty(value = "人员姓名")
    private String peopleName;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String unitPost;
}
