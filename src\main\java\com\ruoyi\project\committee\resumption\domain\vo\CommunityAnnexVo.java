package com.ruoyi.project.committee.resumption.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 公益情况附件视图对象
 */
@Data
@ApiModel("公益情况附件视图对象")
public class CommunityAnnexVo {

    @ApiModelProperty("附件ID")
    private Long id;

    @ApiModelProperty("公益情况ID")
    private String communityId;

    @ApiModelProperty("附件URL")
    private String url;

    @ApiModelProperty("附件名称")
    private String annexName;

    @ApiModelProperty("附件类型")
    private String annexType;
}
