package com.ruoyi.project.proposal.service;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.project.proposal.domain.dto.FileCaseDto;
import com.ruoyi.project.proposal.domain.dto.MergeCaseDto;
import com.ruoyi.project.proposal.domain.vo.*;

/**
 * 提案信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
public interface IProposalService {
    /**
     * 查询提案信息
     * 
     * @param id 提案信息主键
     * @return 提案信息
     */
    public ProposalVo selectProposalById(String id);

    /**
     * 查询提案信息列表分页
     * @param pageParam 查询参数
     * @return ProposalPageVo
     */
    public IPage<ProposalPageVo> selectProposalPage(ProposalPageParamVo pageParam);

    /**
     * 查询后期处理提案信息列表分页
     * @param pageParam 查询参数
     * @return ProposalPageVo
     */
    public IPage<ProposalPageVo> selectPostProcessPage(ProposalPageParamVo pageParam);

    /**
     * 新增提案信息
     * 
     * @param proposalEditVo 提案信息
     * @return 结果
     */
    public int insertProposal(ProposalEditVo proposalEditVo);

    /**
     * 修改提案信息
     * 
     * @param proposalEditVo 提案信息
     * @return 结果
     */
    public int updateProposal(ProposalEditVo proposalEditVo);

    /**
     * 批量更新提案信息
     * @param ids 提案信息id
     * @return 结果
     */
    public int updateProposalAssignStatus(String[] ids);


    /**
     * 删除提案信息信息
     * 
     * @param id 提案信息主键
     * @return 结果
     */
    public int deleteProposalById(Long id);

    /**
     * 根据id查询提案信息
     * @param id 提案id
     * @return ProposalWordVo
     */
    public ProposalWordVo selectProposalWordVoById(String id) throws ParseException;

    /**
     * 综合查询分页
     * @param pageParam 查询参数
     * @return ProposalComprehensivePageVo
     */
    public IPage<ProposalComprehensivePageVo> selectComprehensiveProposalPage(ProposalPageParamVo pageParam);

    /**
     * 综合查询
     * @param id 提案id
     * @return ProposalComprehensiveVo
     */
    ProposalComprehensiveVo selectComprehensiveInfoById(String id);


    /**
     * 提案交办
     * 提案交办分页
     * @param pageParam 查询参数
     * @return ProposalAssignPageVo
     */
    IPage<ProposalAssignPageVo> selectProposalAssignPage(ProposalPageParamVo pageParam);


    /**
     * 获取提案提案人员
     * @param proposer 提案人员
     * @return String
     */
    String getProposer(String proposer);

    /**
     * 批量删除提案信息
     * @param ids 提案id
     * @return int
     */
    int batchDeleteProposal(String[] ids);


    /**
     * 获取提案人员map
     * @param proposalIdList 提案id列表
     * @return Map<String, String>
     */
    Map<String, String> getProposerMap(List<String> proposalIdList);

    /**
     * 获取监督信息分页
     * @param pageParam 查询参数
     * @return 督察数据
     */
    IPage<SupervisionPageVo> getSupervisionPage(ProposalPageParamVo pageParam);

    /**
     * 催办
     * @param id 提案id
     * @return int
     */
    Integer urge(String id);


    /**
     * 获取提案数量
     * @param year 年份
     * @return 数量
     */
    Long getProposalCount(Integer year);

    /**
     * 获取用户提案
     *
     * @return 用户提案
     */
    IPage<ProposalComprehensivePageVo> getUserProposal(ProposalPageParamVo pageParam);

    /**
     * 删除提案信息
     * @param id 提案id
     * @return  Boolean
     */
    Boolean deleteProposal(String id);

    /**
     * 提案立案
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    Boolean fileCase(FileCaseDto fileCaseDto);

    /**
     * 提案立案
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    Boolean notFileCase(FileCaseDto fileCaseDto);

    /**
     * 并案id
     * @param mergeCaseDto mergeCaseDto
     * @return Boolean
     */
    Boolean mergeCase(MergeCaseDto mergeCaseDto);

    /**
     * 提案撤案
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    Boolean cancelCase(FileCaseDto fileCaseDto);

    /**
     * 提案交办
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    Boolean assignCase(FileCaseDto fileCaseDto);

    /**
     * 获取提案立案信息
     * @param proposalId proposalId
     * @return FileCaseVo
     */
    FileCaseVo getFileCaseInfo(String proposalId);

    /**
     * 提案-改办
     * @param fileCaseDto fileCaseDto
     * @return Boolean
     */
    Boolean changeHandle(FileCaseDto fileCaseDto);
}
