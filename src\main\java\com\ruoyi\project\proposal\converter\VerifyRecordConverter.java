package com.ruoyi.project.proposal.converter;

import com.ruoyi.project.proposal.domain.ProposalVerifyRecord;
import com.ruoyi.project.proposal.domain.vo.ProposalVerifyRecordEditVo;
import com.ruoyi.project.proposal.domain.vo.ProposalVerifyRecordVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface VerifyRecordConverter {

    VerifyRecordConverter INSTANCE = Mappers.getMapper(VerifyRecordConverter.class);

    ProposalVerifyRecordVo convert(ProposalVerifyRecord proposalVerifyRecord);

    ProposalVerifyRecord convert(ProposalVerifyRecordEditVo proposalVerifyRecordEditVo);

}
